﻿using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Microsoft.Graph;
using Microsoft.Graph.Models;
using Microsoft.Graph.Users;
using Microsoft.Graph.Invitations;
using Microsoft.Kiota.Abstractions.Authentication;
using Serilog;
using System;
using System.Collections.Generic;
using System.Net;
using System.Net.Http.Headers;
using System.Threading;
using System.Threading.Tasks;
using WHO.MALARIA.Common.Services;
using WHO.MALARIA.Domain.Enum;
using WHO.MALARIA.Domain.Exceptions;
using WHO.MALARIA.Features;

namespace WHO.MALARIA.Services
{
    /// <summary>
    /// Define methods which are used for graph api service implementation
    /// </summary>
    public interface IGraphService
    {
        Task<UserExistsInAzureDirectoryStatus> IsUserExistingInAzureAD(string searchtext, string currentUsername);

        Task<Tuple<SendInvitationStatus, string>> SendInvitation(string firstName, string lastName, string recipientEmailAddress, string bodyMessage, string redirectUrl, string currentUsername, string _culture);

        Task<GraphApiTestResult> TestGraphApiAccess(string currentUsername);
    }
    public class GraphService : IGraphService
    {
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly ILogger<GraphService> _logger;
        private readonly ITranslationService _translationService;

        public GraphService(IHttpContextAccessor httpContextAccessor, ILogger<GraphService> logger, ITranslationService translationService)
        {
            _httpContextAccessor = httpContextAccessor;
            _logger = logger;
            _translationService = translationService;
        }

        /// <summary>
        /// Check user existence in azure AD by matching  email or user principal name with given search text in azure AD
        /// </summary>
        /// <param name="searchText"> Email address of the user to check existence in azure AD </param>
        /// <param name="currentUsername"> Current logged in username </param>
        /// <returns> Returns statuses as user exists, does not exists or insufficient privileges to add user </returns>
        public async Task<UserExistsInAzureDirectoryStatus> IsUserExistingInAzureAD(string searchText, string currentUsername)
        {
            try
            {
                GraphServiceClient graphServiceClient = GetGraphClient(currentUsername);

                if (graphServiceClient != null)
                {
                    var userResponse = await graphServiceClient.Users
                                    .GetAsync(requestConfiguration => {
                                        requestConfiguration.QueryParameters.Filter = $"mail eq '{searchText}' or UserPrincipalName eq '{searchText}'";
                                    });

                    if (userResponse?.Value != null && userResponse.Value.Count > 0)
                    {
                        return UserExistsInAzureDirectoryStatus.Exists;
                    }
                }

                return UserExistsInAzureDirectoryStatus.DoesNotExists;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, ex.Message);

                if (ex is Microsoft.Kiota.Abstractions.ApiException apiException)
                {
                    if (apiException.ResponseStatusCode == (int)HttpStatusCode.Unauthorized ||
                        apiException.ResponseStatusCode == (int)HttpStatusCode.Forbidden)
                    {
                        return UserExistsInAzureDirectoryStatus.InSufficientPrivilegesToAddExistingWHOUser;
                    }
                }

                return UserExistsInAzureDirectoryStatus.InsufficientPrivileges;
            }
        }

        /// <summary>
        ///  Send invitation to  user by  invoking the Azure B2B Guest invitation API to invite an external user and add into azure AD as guest
        ///  </summary>
        /// <param name="firstName">Guest user first name</param>
        /// <param name="lastName">Guest user last name</param>s
        /// <param name="recipientEmailAddress">Guest user email address</param>
        /// <param name="bodyMessage">Message sent to invited user</param>
        /// <param name="redirectUrl">After accepting invitation user will be redirected to specified redirect url</param>
        /// <param name="currentUsername">Current loggedin username</param>
        /// <param name="_culture">Culture code to send email in current culture</param>
        /// <returns>Invitation status and invitation redeem Url</returns>
        public async Task<Tuple<SendInvitationStatus, string>> SendInvitation(string firstName, string lastName, string recipientEmailAddress, string bodyMessage, string redirectUrl, string currentUsername, string _culture)
        {
            try
            {
                Invitation invitationResponse = null;
                GraphServiceClient graphServiceClient = GetGraphClient(currentUsername);

                if (string.IsNullOrEmpty(_culture))
                {
                    _culture = Thread.CurrentThread.CurrentCulture.TwoLetterISOLanguageName;
                }

                if (graphServiceClient != null)
                {
                    var invitation = new Invitation
                    {
                        InviteRedirectUrl = redirectUrl,
                        InvitedUserDisplayName = $"{firstName} {lastName}",
                        InvitedUserEmailAddress = recipientEmailAddress,
                        InvitedUserMessageInfo = new InvitedUserMessageInfo
                        {
                            CustomizedMessageBody = bodyMessage,
                            MessageLanguage = _culture
                        },
                        SendInvitationMessage = false
                    };

                    invitationResponse = await graphServiceClient.Invitations.PostAsync(invitation);
                }
                return Tuple.Create(SendInvitationStatus.InvitedSuccesfully, invitationResponse?.InviteRedeemUrl ?? string.Empty);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, ex.Message);

                if (ex is Microsoft.Kiota.Abstractions.ApiException apiException &&
                    apiException.ResponseStatusCode == (int)HttpStatusCode.BadRequest)
                {
                    return Tuple.Create(SendInvitationStatus.BadRequest, string.Empty);
                }
                return Tuple.Create(SendInvitationStatus.InsufficientPrivileges, string.Empty);
            }
        }

        private GraphServiceClient GetGraphClient(string currentUsername)
        {
            GraphServiceClient graphServiceClient = null;
            string key = $"{Domain.Constants.Constants.Common.ActiveDirectoryUserDetailsKey}_{currentUsername.ToLower()}";
            string accessToken = _httpContextAccessor.HttpContext.Request.Cookies["access_token"];

            if (!string.IsNullOrWhiteSpace(accessToken))
            {
                Log.Information("Access token GetGraphClient - " + accessToken);
                Log.Information("Access token Startup GetGraphClient - " + key);

                var tokenProvider = new BaseBearerTokenAuthenticationProvider(
                    new TokenProvider(accessToken));

                graphServiceClient = new GraphServiceClient(tokenProvider);
            }
            else
            {
                Log.Warning("No access token found in cookies for user: " + currentUsername);
            }

            return graphServiceClient;
        }

        /// <summary>
        /// Test method to get detailed information about Graph API permissions and access
        /// </summary>
        /// <param name="currentUsername">Current user's username</param>
        /// <returns>Detailed information about Graph API access</returns>
        public async Task<GraphApiTestResult> TestGraphApiAccess(string currentUsername)
        {
            var result = new GraphApiTestResult
            {
                Username = currentUsername,
                HasAccessToken = false,
                Tests = new List<GraphApiTest>()
            };

            try
            {
                string accessToken = _httpContextAccessor.HttpContext.Request.Cookies["access_token"];
                result.HasAccessToken = !string.IsNullOrWhiteSpace(accessToken);

                if (!result.HasAccessToken)
                {
                    result.Tests.Add(new GraphApiTest
                    {
                        TestName = "AccessToken",
                        Success = false,
                        ErrorMessage = "No access token found in cookies"
                    });
                    return result;
                }

                GraphServiceClient graphServiceClient = GetGraphClient(currentUsername);
                if (graphServiceClient == null)
                {
                    result.Tests.Add(new GraphApiTest
                    {
                        TestName = "GraphClient",
                        Success = false,
                        ErrorMessage = "Failed to create GraphServiceClient"
                    });
                    return result;
                }

                // Test 1: Get current user
                try
                {
                    var me = await graphServiceClient.Me.GetAsync();
                    result.Tests.Add(new GraphApiTest
                    {
                        TestName = "GetCurrentUser",
                        Success = true,
                        Details = $"User: {me?.UserPrincipalName}, DisplayName: {me?.DisplayName}"
                    });
                }
                catch (Exception ex)
                {
                    result.Tests.Add(new GraphApiTest
                    {
                        TestName = "GetCurrentUser",
                        Success = false,
                        ErrorMessage = ex.Message,
                        StatusCode = GetApiExceptionStatusCode(ex)
                    });
                }

                // Test 2: List users (requires User.Read.All)
                try
                {
                    var users = await graphServiceClient.Users.GetAsync(requestConfiguration =>
                    {
                        requestConfiguration.QueryParameters.Top = 1;
                    });
                    result.Tests.Add(new GraphApiTest
                    {
                        TestName = "ListUsers",
                        Success = true,
                        Details = $"Retrieved {users?.Value?.Count ?? 0} users"
                    });
                }
                catch (Exception ex)
                {
                    result.Tests.Add(new GraphApiTest
                    {
                        TestName = "ListUsers",
                        Success = false,
                        ErrorMessage = ex.Message,
                        StatusCode = GetApiExceptionStatusCode(ex)
                    });
                }

                // Test 3: Try to create a test invitation (this is what's failing)
                try
                {
                    var invitation = new Microsoft.Graph.Models.Invitation
                    {
                        InviteRedirectUrl = "https://test.com",
                        InvitedUserDisplayName = "Test User",
                        InvitedUserEmailAddress = "<EMAIL>",
                        SendInvitationMessage = false
                    };

                    // This will fail if we don't have User.Invite.All permission
                    var invitationResponse = await graphServiceClient.Invitations.PostAsync(invitation);
                    result.Tests.Add(new GraphApiTest
                    {
                        TestName = "CreateInvitation",
                        Success = true,
                        Details = $"Test invitation created successfully"
                    });
                }
                catch (Exception ex)
                {
                    result.Tests.Add(new GraphApiTest
                    {
                        TestName = "CreateInvitation",
                        Success = false,
                        ErrorMessage = ex.Message,
                        StatusCode = GetApiExceptionStatusCode(ex)
                    });
                }

            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during Graph API testing");
                result.Tests.Add(new GraphApiTest
                {
                    TestName = "General",
                    Success = false,
                    ErrorMessage = ex.Message
                });
            }

            return result;
        }

        private int? GetApiExceptionStatusCode(Exception ex)
        {
            if (ex is Microsoft.Kiota.Abstractions.ApiException apiException)
            {
                return apiException.ResponseStatusCode;
            }
            return null;
        }

        private class TokenProvider : IAccessTokenProvider
        {
            private readonly string _accessToken;

            public TokenProvider(string accessToken)
            {
                _accessToken = accessToken;
            }

            public Task<string> GetAuthorizationTokenAsync(Uri uri, Dictionary<string, object>? additionalAuthenticationContext = null, CancellationToken cancellationToken = default)
            {
                return Task.FromResult(_accessToken);
            }

            public AllowedHostsValidator AllowedHostsValidator { get; } = new AllowedHostsValidator();
        }
    }

    /// <summary>
    /// Result of Graph API testing
    /// </summary>
    public class GraphApiTestResult
    {
        public string Username { get; set; }
        public bool HasAccessToken { get; set; }
        public List<GraphApiTest> Tests { get; set; } = new List<GraphApiTest>();
    }

    /// <summary>
    /// Individual Graph API test result
    /// </summary>
    public class GraphApiTest
    {
        public string TestName { get; set; }
        public bool Success { get; set; }
        public string ErrorMessage { get; set; }
        public string Details { get; set; }
        public int? StatusCode { get; set; }
    }
}
