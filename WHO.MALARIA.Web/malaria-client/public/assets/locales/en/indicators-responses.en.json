{"app": {"DR_Objective_1_Indicator_1_1_1_Title": "Malaria Toolkit - Indicator 1.1.1 Response", "DR_Objective_1_Indicator_1_1_2_Title": "Malaria Toolkit - Indicator 1.1.2 Response", "DR_Objective_1_Indicator_1_1_3_Title": "Malaria Toolkit - Indicator 1.1.3 Response", "DR_Objective_1_Indicator_1_1_4_Title": "Malaria Toolkit - Indicator 1.1.4 Response", "DR_Objective_1_Indicator_1_1_5_Title": "Malaria <PERSON>lkit - Indicator 1.1.5 Response", "DR_Objective_1_Indicator_1_1_6_Title": "Malaria <PERSON>lkit - Indicator 1.1.6 Response", "DR_Objective_1_Indicator_1_1_7_Title": "Malaria Toolkit - Indicator 1.1.7 Response", "DR_Objective_1_Indicator_1_1_8_Title": "Malaria Toolkit - Indicator 1.1.8 Response", "DR_Objective_1_Indicator_1_1_9_Title": "Malaria <PERSON>lkit - Indicator 1.1.9 Response", "DR_Objective_1_Indicator_1_3_1_Title": "Malaria Toolkit - Indicator 1.3.1 Response", "DR_Objective_1_Indicator_1_3_2_Title": "Malaria Toolkit - Indicator 1.3.2 Response", "DR_Objective_1_Indicator_1_3_3_Title": "Malaria Toolkit - Indicator 1.3.3 Response", "DR_Objective_1_Indicator_1_3_4_Title": "Malaria Toolkit - Indicator 1.3.4 Response", "DR_Objective_1_Indicator_1_3_5_Title": "Malaria <PERSON>lkit - Indicator 1.3.5 Response", "DR_Objective_1_Indicator_1_3_6_Title": "Malaria <PERSON>lkit - Indicator 1.3.6 Response", "DR_Objective_1_Indicator_1_3_7_Title": "Malaria Toolkit - Indicator 1.3.7 Response", "DR_Objective_2_Indicator_2_1_1_Title": "Malaria Toolkit - Indicator 2.1.1 Response", "DR_Objective_2_Indicator_2_1_2_Title": "Malaria Toolkit - Indicator 2.1.2 Response", "DR_Objective_2_Indicator_2_1_3_Title": "Malaria Toolkit - Indicator 2.1.3 Response", "DR_Objective_2_Indicator_2_1_4_Title": "Malaria Toolkit - Indicator 2.1.4 Response", "DR_Objective_2_Indicator_2_2_1_Title": "Malaria Toolkit - Indicator 2.2.1 Response", "DR_Objective_2_Indicator_2_2_2_Title": "Malaria Toolkit - Indicator 2.2.2 Response", "DR_Objective_2_Indicator_2_2_3_Title": "Malaria Toolkit - Indicator 2.2.3 Response", "DR_Objective_2_Indicator_2_2_4_Title": "Malaria Toolkit - Indicator 2.2.4 Response", "DR_Objective_2_Indicator_2_2_5_Title": "Malaria <PERSON>lkit - Indicator 2.2.5 Response", "DR_Objective_2_Indicator_2_2_6_Title": "Malaria <PERSON>lkit - Indicator 2.2.6 Response", "DR_Objective_2_Indicator_2_3_1_Title": "Malaria Toolkit - Indicator 2.3.1 Response", "DR_Objective_2_Indicator_2_3_2_Title": "Malaria Toolkit - Indicator 2.3.2 Response", "DR_Objective_2_Indicator_2_4_1_Title": "Malaria Toolkit - Indicator 2.4.1 Response", "DR_Objective_2_Indicator_2_4_2_Title": "Malaria <PERSON>lkit - Indicator 2.4.2 Response", "DR_Objective_2_Indicator_2_4_4_Title": "Malaria Toolkit - Indicator 2.4.4 Response", "DR_Objective_2_Indicator_2_5_1_Title": "Malaria <PERSON>lkit - Indicator 2.5.1 Response", "DR_Objective_3_Indicator_3_2_1_Title": "Malaria Toolkit - Indicator 3.2.1 Response", "DR_Objective_3_Indicator_3_2_2_Title": "Malaria Toolkit - Indicator 3.2.2 Response", "DR_Objective_3_Indicator_3_2_3_Title": "Malaria Toolkit - Indicator 3.2.3 Response", "DR_Objective_3_Indicator_3_3_1_Title": "Malaria Toolkit - Indicator 3.3.1 Response", "DR_Objective_3_Indicator_3_3_2_Title": "Malaria <PERSON>lkit - Indicator 3.3.2 Response", "DR_Objective_3_Indicator_3_3_3_Title": "Malaria <PERSON>lkit - Indicator 3.3.3 Response", "DR_Objective_3_Indicator_3_4_1_Title": "Malaria <PERSON>lkit - Indicator 3.4.1 Response", "DR_Objective_3_Indicator_3_4_2_Title": "Malaria <PERSON>lkit - Indicator 3.4.2 Response", "DR_Objective_3_Indicator_3_5_1_Title": "Malaria <PERSON>lkit - Indicator 3.5.1 Response", "DR_Objective_3_Indicator_3_5_2_Title": "Malaria <PERSON>it - Indicator 3.5.2 Response", "DR_Objective_3_Indicator_3_6_1_Title": "Malaria <PERSON>it - Indicator 3.6.1 Response", "DR_Objective_4_Indicator_4_1_1_Title": "Malaria Toolkit - Indicator 4.1.1 Response", "DR_Objective_4_Indicator_4_1_2_Title": "Malaria Toolkit - Indicator 4.1.2 Response", "DR_Objective_4_Indicator_4_1_3_Title": "Malaria Toolkit - Indicator 4.1.3 Response", "DR_Objective_4_Indicator_4_2_1_Title": "Malaria Toolkit - Indicator 4.2.1 Response", "DR_Objective_4_Indicator_4_2_2_Title": "Malaria Toolkit - Indicator 4.2.2 Response", "DR_Objective_4_Indicator_4_3_1_Title": "Malaria Toolkit - Indicator 4.3.1 Response", "DR_Objective_4_Indicator_4_4_1_Title": "Malaria Toolkit - Indicator 4.4.1 Response", "DR_Objective_4_Indicator_4_4_2_Title": "Malaria <PERSON>lkit - Indicator 4.4.2 Response", "DR_Objective_4_Indicator_4_4_3_Title": "Malaria <PERSON>lkit - Indicator 4.4.3 Response"}, "Common": {"YearMonthDate": "yyyy/mm/dd", "Add": "Add", "Cancel": "Cancel", "Save": "Save", "Yes": "Yes", "No": "No", "True": "True", "Countries": "Countries", "Finalize": "Finalize", "Year": "Year", "Line": "Line", "ReasonForChanges": "Reasons for changes observed over time", "ReasonForChangesByRegion": "Reasons for difference observed between regions", "Horizontal": "Horizontal", "Vertical": "vertical", "GenerateGraph": "Generate Graph", "IndicatorNoAssess": "This Indicator cannot be assessed", "IndicatorNoAssessReasons": "Reasons for not being able to assess", "National": "National", "Public": "Public", "Private": "Private", "IndicatorDisaggregatedByHealthSector": "This indicator can be disaggregated by health sector (public/private).", "NationalLevelDataRequired": "National level data is required to complete the assessment of this indicator.", "DataAreNotUsed": "Data are not used", "NoteToCompleteTheAssessment": "Data from the national level system must be entered to complete the assessment of this indicator.", "DataUse": "Data Use", "Evidence": "Evidence", "Details": "Details", "Links": "Links", "DetailsForBothResponses": "Add details for both responses (whether yes or no is checked)", "Rate": "Rate (%)", "ProvideDetails": "Provide details", "Hospital": "Hospital", "Community": "Community", "Both": "Both", "Percentage": "Percentage", "Status": "Status", "Met": "Met", "NotMet": "Not met", "PartiallyMet": "Partially met", "NotAssessed": "Not Assessed", "IndicatePercentage": "Indicate the percentage", "NoInformationSystemExists": "No information system exists", "CompleteAssessmentOfIndicator": "User must complete all sections for this indicator to be considered as completely assessed", "Explain": "Explain", "HealthFacilities": "Health facilities", "Laboratory": "Laboratory", "FaithBasedClinics": "Faith-based clinics", "NonGovernmentalOrganizationClinics": "Non-governmental organization clinics", "Military": "Military", "Police": "Police", "Prison": "Prison", "PrivateInformal": "Private - informal", "ReasonForNoAssessed": "Provide reason only if cannot be assessed (if number is not entered)", "HealthFacilitiesWithStockOuts": "No. of health facilities with stock outs", "HealthFacilitiesReporting": "Number of health facilities reporting ", "ProportionHealthFacilitiesWithStockOuts": "Proportion of health facilities with stock outs", "CannotBeAssessed": "Cannot be assessed", "NoteToCompleteAssessment": "Information for at least one tool should be completed to complete the assessment for this indicator", "Total": "total", "NoOfRows": "No. of Rows", "PrivateFormal": "Private - formal", "PrivateFormalText": "Private formal", "SMCNoOfChildrenTargeted": "Number of children in areas targeted for SMC in a transmission season", "SMCNoOfChildrenTreatedAQSP1Cycle": "Number of children treated with (AQ+SP) 1st cycle of SMC in a transmission season", "SMCNoOfChildrenTreatedAQSP2Cycle": "Number of children treated with (AQ+SP) 2nd cycle of SMC in a transmission season", "SMCNoOfChildrenTreatedAQSP3Cycle": "Number of children treated with (AQ+SP) 3rd cycle of SMC in a transmission season", "SMCNoOfChildrenTreatedAQSP4Cycle": "Number of children treated with (AQ+SP) 4th cycle of SMC in a transmission season", "SMCNoOfChildrenExcluded": "Number of children excluded due to previous side", "SMCNoOfChildrenInCycle": "The number of children seen in the cycle", "MDATargetedMassDrugAdministration": "Number of people targeted for Mass Drug Administration", "MDATreatedWithDHAPPerArea": "Number of individuals treated with DHAP per MDA round per area", "MDAIndividualsExcluded": "Individuals excluded and reason", "MDAAdverseDrugReactions": "Adverse drug reactions", "MDAResponseDescProportion": "Using the list of variables recorded from each tool, calculate the proportion of MDA variables recorded:", "MDANoteToCompleteAssessmentChecklist": "The checklists should be completed for all variables listed to complete the assessment of this indicator", "IRSPeopleProtected": "Number of people protected by IRS in the previous 12 months", "IRSPeopleTargeted": "Number of people in the targeted risk group", "IRSTargetedStructures": "Number of targeted structures", "IRSSprayedStructures": "Number of structures sprayed", "IRSPopulationAtRisk": "Population at risk", "DetailsInformationSystem": "Details for at least one information system should be completed to complete the assessment of this indicator.", "GuideLineDetailInformation": "Guideline name and Health sector distribution information must be filled in for this indicator to be assessed to complete the assessment of this indicator", "LarvalSourceManagement": "Larval source management", "LarvalHabitatManipulation": "Habitat manipulation and biological larviciding", "LarvalHabitatManipulationModification": "Habitat manipulation and habitat modification", "LarvalHabitatManipulationLarviciding": "Habitat manipulation and larviciding", "LarvalBiologicalLarviciding": "Biological larviciding and larviciding", "LarvalHabitatModificationLarviciding": "Habitat modification and larviciding", "DrugEfficacyResponseDesc": "Using the list of variables recorded from each tool, calculate the proportion of drug efficacy variables recorded: ", "DrugEfficacySentinelSites": "Number of sentinel sites", "DrugEfficacyPatientsETFLCFLPF": "Number of patients with ETF + LCF (PCR corrected) + LPF (PCR corrected)", "DrugEfficacyPatientsAdequateClinicalResponse": "Number of patients classified as having adequate clinical and parasitological response at end of follow-up (day 28 or 42) + number of patients with ETF + LCF (PCR corrected) + LPF (PCR corrected)", "DrugEfficacyPatientsTreatmentFailure28": "Number of patients with treatment failure at day 28 (specify drug used) per study", "DrugEfficacyPatientsTreatmentFailure42": "Number of patients with treatment failure at day 42 (specify drug used) per study", "DrugEfficacyPatientsFollowedUp28": "Total number of patients followed-up on day 28 (specify drugs)", "DrugEfficacyPatientsFollowedUp42": "Total number of patients followed-up on day 42 (specify drugs)", "DrugEfficacyPatientsPfParasites": "Number of patients with P.f parasites at day 3", "DrugEfficacyPatientsFollowedPfParasites": "Number of patients with P.f parasite followed up on day 3", "DrugEfficacyPatientsWithdrawn": "Number of patients lost to follow-up or withdrawn", "DrugEfficacyStudiesConductedOnLineTreatment1": "Number of studies conducted on 1st line treatment in the last 24 month (specify drug used)", "DrugEfficacyStudiesConductedOnLineTreatment2": "Number of studies conducted on 2nd line treatment in the last 24 months (specify drug used)", "DrugEfficacyTreatmentPolicyChanged": "Treatment policy changed (Yes / no (Country has revised the treatment policy in the last year)", "Number": "No.", "Under5": "Under 5", "Over5": "Over 5", "Sex": "Sex", "PregnantWoman": "Pregnant woman", "HealthSector": "Health sector (Public, private community)", "Geography": "Geography", "Other": "Other", "VariableNumber": "Variable No.", "TableFooterTitle": "% of WHO recommended variables recorded ", "NameOfReportingToolSourceDocument": "Name of recording tool/ source document", "ToolType": "Tool Type", "ToolTypePlaceholder": "e.g. electronically on an internet-based system or on a local system, paper registers, etc.) and the device and version if applicable", "YearToolWasIntroduced": "Year tool was introduced", "YearToolPlaceholder": "e.g. 2015", "RecipientListVariablesReportedPlaceholder": "names of variables e.g. suspected, presumed, confirmed cases", "RecipientOfReports1Placeholder": "[ role] e.g. health care workers, technician, volunteers", "NationalRequired": "National Required", "SubNationalRequired": "Subnational Required", "ServiceDeliveryRequired": "Service Delivery Required", "CurrentlyAvailable": "Currently Available", "ProportionAvailable": "Proportion available", "Disagregation": "Disaggregation", "Indicators": "Indicators", "LarvalDetailsInformationSystem": "At least one guideline must be filled in for this indicator to be assessed to complete the assessment of this indicator", "BurdenNoteToCompleteTheAssessment": "At least one of the below should be ticked with details completed to complete the assessment of this indicator.", "BurdenReductionNote": "At least one of the below should be ticked with details completed to complete the assessment of this indicator.", "ClickToView": "Click to view", "Delete": "Delete", "ResponseProportionError": "Proportion value should be from 0 to 100", "Criteria": "Criteria met", "Region": "Region", "Name": "Name", "SrNo": "Variable No.", "DocumentsDetails": "Documents developed and used", "DocumentsData": "Documents and/or data used", "MilitaryRowHeading": "Military hospitals/health centres", "PoliceRowHeading": "Police holding cells/treatment seeking area", "Percent": "Percent %"}, "DRObjective_1_Responses": {"Indicator_1_1_1": {"ResponseDesc": "Indicate the year and percentage for passive care seeking <br/> Please enter year in ascending order", "ResponseDescB": "Indicate the region and percentage for passive care seeking", "ResponseDesc2": "Indicate region and percentage of tested suspects seeking", "MinimumOnePointRequired": "A minimum of one time point should be entered for the most recent year of data to complete the assessment of this indicator.", "TabLabel1": "Figure 1.1.1a Passive care seeking rate over time", "TabLabel2": "Figure 1.1.1b Passive care seeking rate by region of year", "TextBoxlabel": "Reasons for not being able to assess", "NationalLevelEstimate": "Passive care seeking rate (%)", "Region": "Region", "ServicesFreeForMalaria": "Select the services which are free for malaria in the public sector", "ProvideDetails": "Provide Details", "AgeDiffer": "Does this differ by age?", "SpecificPopolationGroup": "Are there any specific population groups with malaria that are diagnosed and treated that are not captured by the national surveillance system? (e.g. populations who do not seek care, certain sectors of the health system or care provider types (traditional medicine), specific sub-groups, etc.)", "ProvideDetail": "Provide details on specific excluded populations and/or care providers that treat malaria cases that are not captured and reasons for exclusion ", "HaveNewStrategyUsed": "For areas with difficult access to health care, have new strategies been used in order to ensure improved access to care (e.g., a CHW in the area who can test and treat symptomatic patients)?", "SeekTreatment": "If an individual (in the forest  fringe, farms or rice fields) has fever where do they typically seek treatment when away from home?", "PleaseSpecify": "Please Specify", "CheckboxOption1": "No treatment", "CheckboxOption2": "Community Health Worker", "CheckboxOption3": "Public Health Facility", "CheckboxOption4": "Private Health Provider", "CheckboxOption5": "Other", "TreatmentOptions": "Treatment Options", "AllServices": "All Services", "Consultation": "Consultation", "Diagnosis": "Diagnosis", "Treatment": "Treatment", "Hospitalisation": "Hospitalisation", "NoOfRows": "No. of Rows", "YearOfData": "Year of Data", "ResponseErrorForCheckBox": "Please Select atleast one checkbox", "CompleteAssessmentDesc": "Please complete parts A-E to finalize the assessment of this indicator", "BarGraphTitle": "Figure 1.1.1b Passive care seeking rate by region of year", "ResponseErrorAToE": "Please complete Step A to E to finalize the indicator", "YearOfDataHeading": "Please enter the most recent year of data to complete the assessment of this indicator"}, "Indicator_1_1_2": {"ResponseError": "Please fill the graph details and generate the graph ", "ResponseDescA": "Indicate the year and percentage for suspects tested <br/> Please enter year in ascending order", "ResponseDescB": "Add the region and percentage of suspects tested", "ProportionOfSuspects": "Figure 1.1.2a  Proportion of suspects tested over time", "ProportionOfSuspectsOverRegion": "Figure 1.1.2b  Proportion of suspects tested by region for year", "ProportionOfSuspectsPercent": " (%)", "MetNotMetTooltip": "For the most recent year of data the proportion of suspected cases tested over time is considered: <br/> Met  >=80% <br/> Partially met  50-79% <br/>  Not met  <50%", "NationalLevelEstimate": "Proportion of suspects tested (%) ", "YearOfDataHeading": "Please enter the most recent year of data to complete the assessment of this indicator"}, "Indicator_1_1_3": {"ResponseDesc": "Enter the percentage of service-delivery points that are included in the surveillance system (i.e., expected to report).", "MetNotMetTooltip": "Service-delivery participation rate is considered: <br/> Met  >=80% <br/> Partially met  50-79% <br/> Not met  <50%"}, "Indicator_1_1_4": {"ResponseDesc": "Enter the percentage (%) of service-delivery points included in the system that report* routinely (e.g. for >80% of the months in 1 year).", "ZeroReportingCases": "*Reporting includes zero cases (zero reporting)", "MetNotMetTooltip": "Service-delivery reporting rate is considered; <br/> Met  >=80% <br/> Partially met  50-79% <br/> Not met  <50%"}, "Indicator_1_1_5": {"ResponseDesc": "Determine the proportion of notified confirmed malaria cases that sought care within 48 hours of symptom onset or as per guidelines. The time between symptom onset and health facility attendance is used to calculate whether cases sought care within 48 hours of symptom onset.", "ResponseError": "Please add National level data and graph details to finalize the indicator", "IndicateRecentYearDataText": "Indicate the proportion of cases that sought care within 48 hours of symptom onset for the most recent year of data", "IndicatorDisaggregatedByHealthSector": "This indicator can be disaggregated by geographical area and health sector (public/private).", "BarGraphTitle": "Figure 1.1.5  Proportion of cases that sought care within 48 hours of symptom onset for year", "MetNotMetTooltip": "Proportion of cases that sought care within 48 hours of symptom onset is considered;  <br/> Met  >=80% <br/> Partially met  50-79% <br/> Not met  <50%", "Region": "Region"}, "Indicator_1_1_6": {"ResponseDesc": "Determine the proportion of parasitologically confirmed malaria cases  that were diagnosed within 24 hours of first attending a health facility. The time between date of health facility attendance   and date of diagnosis is used to calculate whether confirmed cases were diagnosed within 24 hours. Cases must also be recorded as symptomatic.", "DisaggregatedByGeographicalArea": "This indicator can be disaggregated by geographical area and by health sector (public/private). The ability to calculate this indicator will depend on how the data is recorded and reported.", "IndicateRecentYearDataText": "Indicate the proportion of cases that sought care within 48 hours of symptom onset for the most recent year of data", "ProportionOfCases24": "Figure 1.1.6  Proportion of cases with symptoms diagnosed within 24 hours for year", "MetNotMetTooltip": "For the most recent year of data the proportion of cases with symptoms diagnosed within 24 hours is considered;  <br/> Met  >=80% <br/> Partially met  50-79% <br/> Not met  <50%"}, "Indicator_1_1_7": {"ResponseDescA": "Does a national vital registration system exist or is there a sample vital registration system that may be representative of the country?", "ResponseError": "Please select ' This Indicator cannot be assessed.' and provide reasons", "ProvideDetailsOfExistingSystem": "Provide details of existing system", "DeathsSystematicallyRecorded": "How are deaths systematically recorded?", "NMPHaveAccess": "Does the NMP have access to VR data or other mortality data?", "ProvideDetails": "Provide details e.g mode and frequency of access, data variables shared", "DeathRegistration": "Completeness of death registration (%)", "ProportionOfDeaths": "Proportion of deaths with cause of death classified as ill-defined and unknown causes of mortality", "ResponseDescE": "The user needs to be able to replace these figures with their own data to generate the graph", "ResponseDescETitle": "Reported malaria deaths over time at the national level", "ResponseDescENote": "Please enter years in ascending order.", "ResponseARadioActionYes": "If yes is answered then sections A-D must be completed to complete the assessment of this indicator.", "ResponseARadioActionNo": "If the answer is no then indicator 1.1.7 cannot be assessed.", "DataSource1": "DataSource 1", "DataSource2": "DataSource 2", "DataSource3": "DataSource 3", "MalariaInpatientDeaths": "Malaria inpatient deaths(HMIS/MIS)", "MalariaHospitalDeaths": "Malaria hospital deaths(Vital registration)", "TotalMalariaDeaths": " Total malaria deaths(hospital + community)(Vital registration)", "DataSourceOne": "Data Source 1", "DataSourceTwo": "Data Source 2", "DataSourceThree": "Data Source 3", "GraphTitle": "Figure 1.1.7  Reported malaria deaths over time ", "MetNotMetTooltip": "Vital registration system is considered to have high national coverage and quality if: <br/> Both criteria are met = Met <br/> One criteria is met = Partially met <br/> Neither condition is met = Not met", "ResponseErrorAToD": "Please complete Step A to D to finalize the indicator", "Value": "Value", "YearOfData": "Select year of data"}, "Indicator_1_1_8": {"ResponseDesc": "Has a Therapeutic Efficacy Study (TES)/Integrated Drug Efficacy Study (iDES)  been carried out in line with WHO protocol within the last two years?", "ProvideDetails": "Provide details of TES or iDES", "NoOfSentinelSites": "No. of sentinel sites: ", "ProvideSummary": "Provide summary details and link to study if available online", "Year": "Year", "DrugName": "Drug Name", "TreatmentFailurePatientsDay28": "% of patients with treatment failure at day 28", "TreatmentFailurePatientsDay42": "% of patients with treatment failure at day 42", "PositivePatientOnDay3": "% of patients positive on day 3 (elimination settings only)", "FailureRate": "<10% failure rate at day 28 and 42 documented for all drugs tested and used for treatment", "ResponseRadioActionYes": "All parts should be completed to complete the assessment of this indicator.", "ResponseRadioActionNo": "If the answer is no then this indicator cannot be assessed.", "MetNotMetTooltip": "Met/Not Met Criteria - If treatment failure rate at day 28 AND day 42 is less than 10% for 100% of drugs tested and used for treatment, then this is Met. <br/> If treatment failure rate at day 28 OR day 42 is more than 10% for any of the drugs tested and used for treatment, then this is Partially met.<br/> If treatment failure rate at day 28 AND day 42 is more than 10% for any of the drugs tested and used for treatment, then this is Not met."}, "Indicator_1_1_9": {"MolecularAnalysisDesc": "Molecular analysis is considered carried out if results are available", "MutationsDetectedResults": "Provide details on method, frequency, list of mutations routinely detected and which drugs they confer resistance to, results from any mutations detected", "MolecularAnalysisSelection": "Is molecular analysis carried out for monitoring drug resistance?", "EntoResponseDesc": "If entomology has been selected as a malaria control strategy then this indicator should be assessed for insecticide resistance", "EntoMolecularAnalysisSelection": "Is molecular analysis carried out for monitoring insecticide resistance?", "MetNotMetTooltip": "This indicator is considered;<br/> Met if  molecular analysis carried out for monitoring drug resistance= Yes<br/> Not met if  molecular analysis carried out for monitoring drug resistance= No"}, "Indicator_1_3_1": {"ResponseDesc": "For each example below, find evidence of surveillance data being used for strategic, policy and/or operational planning in the previous 36 months. <br/> If completing this table through a key informant interview, ask to be shown evidence or documentation", "CompleteAssessmentDesc": "To complete the assessment of this indicator at least one of the below should be ticked with details completed .", "NationalStrategicPlanning": "National strategic planning", "SubnationalStrategicPlanning": "Subnational strategic planning", "StratificationPrioritizationInterventions": "Stratification and prioritization of interventions", "MalariaPolicy": "Develop or revise a malaria policy", "AdvocateForPolicy": "Advocate for policy or programme", "MonitorProgramPerformance": "Monitor program performance", "AllocationOfResources": "Allocation of resources", "DistributionOfCommodities": "Distribution of commodities", "EliminationCertification": "Subnational or national elimination certification", "CaseDetection": "Proactive and reactive case detection", "ITPIResponseDesc": "For each example below, find evidence of IPTi data being used for strategic, policy and/or operational planning in the previous 36 months. <br/> If completing this table through a key informant interview, ask to be shown evidence or documentation", "ITPIResponseTitle": " At least one of the below should be ticked with details completed to complete the assessment of this indicator.", "ITPIDistributionOfSulfadoxine": "Distribution of sulfadoxine-pyrimethamine", "IptpDistribution": "Distribution of sulfadoxine-pyrimethamine", "ITNRoutineDistribution": "Distribution of ITNs", "GenomicDiagnostics": "Change in diagnostics", "GenomicTreatment": "Change in treatment policy", "DistributionOfIRS": "Distribution of IRS", "CommodityDistributionAct": "Distribution of ACTs", "CommodityDistributionRDT": "Distribution of RDTs", "ResponseError": "Please select 'Yes'/'No' for at least one evidence of data use and provide details to finalize the indicator.", "MetNotMetTooltip": "Data use for strategic, policy and operational planning is considered;<br/> Met - if data is used ( √)  for at least one of the  responses for strategic planning AND one of the responses for policy AND one of the responses for operational activities. In elimination settings in addition to the above, data should also be used for either subnational or national certificaiton OR proactive and reactive case detection for this indicator to be met. <br/> Partially met - if data is used  ( √)or at least one of the  responses for strategic planning OR  one of the responses for policy ORone of the responses for operational activities. In elimination settings in addition to the above, data should also be used for either subnational or national certificaiton OR proactive and reactive case detection for this indicator to be partially met. <br/> Not met -  if data has not been used (× for all responses). ", "NoteToCompleteTheAssessment": "At least one of the below should be ticked with details completed for this indicator to be considered to be assessed ", "IPTPResponseDesc": "For each example below, find evidence of IPTp data being used for strategic, policy and/or operational planning in the previous 36 months. <br/> If completing this table through a key informant interview, ask to be shown evidence or documentation", "LarvalResponseDesc": "For each example below, find evidence of larval source management data being used for strategic, policy and/or operational planning in the previous 36 months. <br/> If completing this table through a key informant interview, ask to be shown evidence or documentation", "LarvalResponseTitle": "At least one of the below should be ticked with details completed to complete the assessment of this indicator.", "ResponseDescIdentify": "For each example below, find evidence of commodity tracking data being used for strategic, policy and/or operational planning in the previous 36 months. <br/> If completing this table through a key informant interview, ask to be shown evidence or documentation", "ResponseTitleTicked": "At least one of the below should be ticked with details completed to complete the assessment of this indicator.", "DrugEfficacyResponseDesc": "For each example below, find evidence of drug efficacy data being used for strategic, policy and/or operational planning in the previous 36 months. <br/> If completing this table through a key informant interview, ask to be shown evidence or documentation", "EntomologyResponseDesc": "For each example below, find evidence of entomology data being used for strategic, policy and/or operational planning in the previous 36 months. <br/> If completing this table through a key informant interview, ask to be shown evidence or documentation", "GenomicResponseDesc": "For each example below, find evidence of genomic surveillance data being used for strategic, policy and/or operational planning in the previous 36 months. <br/> If completing this table through a key informant interview, ask to be shown evidence or documentation", "IRSResponseDesc": "For each example below, find evidence of IRS surveillance data being used for strategic, policy and/or operational planning in the previous 36 months. <br/> If completing this table through a key informant interview, ask to be shown evidence or documentation", "ITNMassResponseDesc": "For each example below, find evidence of ITN mass campaign surveillance data being used for strategic,policy and/or operational planning in the previous 36 months. <br/> If completing this table through a key informant interview, ask to be shown evidence or documentation", "ITNRoutineResponseDesc": "For each example below, find evidence of routine ITN surveillance data being used for strategic,policy and/or operational planning in the previous 36 months. <br/> If completing this table through a key informant interview, ask to be shown evidence or documentation", "MDAResponseDesc": "For each example below, find evidence of MDA surveillance data being used for strategic,policy and/or operational planning in the previous 36 months. <br/> If completing this table through a key informant interview, ask to be shown evidence or documentation", "SMCResponseDesc": "For each example below, find evidence of SMC surveillance data being used for strategic,policy and/or operational planning in the previous 36 months. <br/> If completing this table through a key informant interview, ask to be shown evidence or documentation", "DocumentsDetailsDesc": "For examples of documents that can be used as evidence please click the side bar"}, "Indicator_1_3_2": {"ResponseDesc": "For each example below, find evidence at the national level of surveillance data being used to improve the surveillance system in the previous 12 months.<br/> If completing this table through a key informant interview, ask to be shown evidence or documentation", "ResponseError": "Please select 'Yes'/'No' for at least one evidence of data use and provide details to finalize the indicator.", "ImprovementsInSupervision": "Improvements in feedback and supervision", "ImprovementsInDataQuality": "Improvements in data quality", "InitiateSurveillanceTraining": "Initiate surveillance training and/or data analysis and use", "ResponseActivities": "Response activities occur in the likely location of infection", "OneShouldBeTicked": "At least one of the below should be ticked with details completed for this indicator to be considered to be assessed", "CompleteAssessmentTicked": "To complete the assessment of this indicator at least one of the below should be ticked with details completed.", "IptpImprovementFeedback": "Improvements in feedback and supervision?", "IptpInitiateSurveillance": "Initiate surveillance training and/or data analysis and use", "ITPIImprovementsInDataQuality": "Improvements in data quality", "ITPIImprovementInFeedback": "Improvements in feedback and supervision?", "IPTIInitiateSurveillanceTraining": "Initiate surveillance training and/or data analysis and use", "ITPIOneShouldBeTicked": "At least one of the below should be ticked with details completed for this indicator to be considered to be assessed ", "ITNRoutineDesc": "At least one of the below should be ticked with details completed to complete the assessment of this indicator.", "MetNotMetTooltip": "Evidence of data use for decisions to improve surveillance system is considered:<br/> Met if all responses are yes (√)<br/> Partially met if some responses are yes (√)<br/> Not met if all responses are no (x)", "GenomicImprovementInFeedback": "Improvements in feedback and supervision", "GenomicImprovementInDataQuality": "Improvements in data quality", "GenomicSurveillanceTraining": "Initiate surveillance training and/or data analysis and use", "NoteToCompleteTheAssessment": "At least one of the below should be ticked with details completed for this indicator to be considered to be assessed ", "IPTpResponseDec": "If completing this table through a key informant interview, ask to be shown evidence or documentation", "CommodityResponseDesc": "For each example below, find evidence at the national level of commodity tracking surveillance data being used to improve the surveillance system in the previous 12 months.<br/> If completing this table through a key informant interview, ask to be shown evidence or documentation", "DrugEfficacyResponseDesc": "For each example below, find evidence at the national level of drug efficacy data being used to improve the surveillance system in the previous 12 months.<br/> If completing this table through a key informant interview, ask to be shown evidence or documentation", "GenomicResponseDesc": "For each example below, find evidence at the national level of genomic data being used to improve the surveillance system in the previous 12 months.<br/> If completing this table through a key informant interview, ask to be shown evidence or documentation", "IPTIResponseDesc": "For each example below, find evidence at the national level of IPTi data being used to improve the surveillance system in the previous 12 months.<br/> If completing this table through a key informant interview, ask to be shown evidence or documentation", "IPTPResponseDesc": "For each example below, find evidence at the national level of IPTp data being used to improve the surveillance system in the previous 12 months.<br/> If completing this table through a key informant interview, ask to be shown evidence or documentation", "ITNMassResponseDesc": "For each example below, find evidence at the national level of ITN mass campaign data being used to improve the surveillance system in the previous 12 months.<br/> If completing this table through a key informant interview, ask to be shown evidence or documentation", "ITNRoutineResponseDesc": "For each example below, find evidence at the national level of routine ITN data being used to improve the surveillance system in the previous 12 months..<br/> If completing this table through a key informant interview, ask to be shown evidence or documentation", "SMCResponseDesc": "For each example below, find evidence at the national level of SMC data being used to improve the surveillance system in the previous 12 months.<br/> If completing this table through a key informant interview, ask to be shown evidence or documentation", "DocumentsDetailsDesc": "For examples of documents that can be used as evidence please click the side bar", "LinksPlaceholder": "If no link is available collect physical or electronic copy"}, "Indicator_1_3_3": {"ResponseDesc": "Calculate the proportion of review meetings that occurred based on the number of meetings expected and the number of meetings that actually occurred. Countries may have different review schedules and therefore the frequency of meetings should inform the denominator.", "ResponseError": "Please enter No. of meetings occurred and No. of meetings expected for at least one health system level to finalize the indicator", "RecordMeetingPreviousYear": "This indicator should be calculated for every level of the health system. For example, if data review meetings are held at subnational levels (e.g. districts) record the number of meetings each district had in the previous year and the number of meetings expected.", "NoteToCompleteTheAssessment": "At least one of the health system level should be completed to complete the assessment of this indicator. The values can be 0.", "HealthSystemLevel": "Health System Level", "MeetingsOccurred": "No. of meetings occurred (Numerator)", "MeetingsExpected": "No. of meetings expected (Denominator)", "Rate": "Rate (%)", "NationalLevel": "National level", "RegionalLevel": "Regional level", "DistrictLevel": "District level", "SMCResponseDesc": "Calculate the proportion of review meetings that occurred based on the number expected and the number that actually occurred.  Countries may have different review schedules and therefore the frequency of meetings should inform the denominator.", "SMCNoteToCompleteTheAssessment": "This indicator should be calculated for every level of the health system. For example, if data review meetings are held at subnational levels (e.g. districts) record the  number of meetings each district had in the previous year.", "IPTIMeetingsOccurred": "No. of meetings occurred (Numerator)", "IPTIMeetingsExpected": "No. of meetings expected (Denominator)", "ITNRoutineNoteToCompleteTheAssessment": "Note that if ITN data is included in data review meetings of case surveillance data then this does not need to be completed again specifically for ITN", "IRSResponseDesc": "Calculate the proportion of review meetings that occurred based on the number of meetings expected and the number of meetings that actually occurred.  Countries may have different review schedules and therefore the frequency of meetings should inform the denominator. <br/> This indicator should be calculated for every level of the health system. For example, if data review meetings are held at subnational levels (e.g. districts) record the  number of meetings each district had in the previous year.", "IRSNoteToCompleteTheAssessment": "Note that if IRS data is included in data review meetings of case surveillance data then this does not need to be completed again specifically for IRS", "IRSNote2": "At least one of the health system level should be completed  to complete the assessment of this indicator. The values can be 0.", "EntoIfDataIsIncluded": "Note that if entomological data is included in data review meetings of case surveillance data then this does not need to be completed again specifically for entomology", "LarvalNoteToCompleteTheAssessment": "Note that if larval source management data is included in data review meetings of case surveillance data then this does not need to be completed again specifically for larval source management", "EntoAtLeastOne": "At least one of the health system level should be completed  to complete the assessment of this indicator. The values can be 0.", "MetNotMetTooltip": "Proportion of data review meetings that occur is  considered;<br/> Met  >=80%<br/> Partially met  50-79%<br/> Not met  <50%", "IPTPNoteToCompleteTheAssessment": "Note that if IPTp data is included in data review meetings of case surveillance data then this does not need to be completed again specifically for IPTp", "LarvalMeetingsOccurred": "No. of meetings occurred <br> (Numerator)", "LarvalMeetingsExpected": "No. of meetings expected <br> (Denominator)", "SMCMeetingsExpected": "Note that if SMC data is included in data review meetings of case surveillance data then this does not need to be completed again specifically for SMC", "MeetingsExpectedDesc": "If no. of meetings occured exceeds expected then please adjust the meetings expected to be the same as the meetings occured"}, "Indicator_1_3_4": {"ResponseDesc": "Calculate the following proportions at each level of the health system <br/> a. Monthly bulletins produced out of those expected <br/> b. Weekly/monthly malaria epidemic monitoring graphs produced out of those expected ", "ResponseError": "Please enter data for at least one health system level to finalize the indicator.", "ResponseErrors": "Please select 'Yes' or 'No' for Annual Malaria Surveillance Report.", "RoutineOutputsProduced": "Routine outputs produced", "AnnualSurveillanceReport": "Annual malaria report", "OtherAnalyticalOutputs": "Other analytical outputs", "Tab1ResponseDesc": "All sections should be completed to complete the assessment of this indicator. For the calculated tables at least one row must be completed and the values can be 0", "National": "National", "Regional": "Region", "District": "District", "HealthSystemLevel": "Health System Level", "NoOfMonths": "No. of months (Denominator)", "NoOfWeeksMonths": "No. of weeks or months (Denominator)", "MonthlyBulletins": "No. of monthly bulletins produced in the previous year (Numerator)", "EpidemicMonitoringGraph": "No. of weekly/monthly epidemic monitoring graph (Numerator)", "Tab2ResponseDesc": "All sections should be completed for this indicator to be considered to be assessed.", "Tab2ResponseNote": "Has an annual malaria report been produced in the last 12 months?", "Tab3ResponseDesc": "All sections should be completed for this indicator to be considered to be assessed apart from the box which lists details of other analysis.", "Tab3ResponseNote": "Is there a map of the country with stratification based on malaria incidence/number of cases/malariogenic potential?", "SpecifyLastYearUpdate": "Verify and specify last year of update", "ProvideDetailsOtherAnalysis": "List and provide details of other analysis", "HighRiskPopulations": "Have high-risk populations been characterized and described for the country? e.g., age, sex, occupation, location, mobility", "NumberOfWeeksMonths": "Number of weeks or months <br/> (Denominator)", "ITPIResponseDesc": "Calculate the following proportions for IPTi surveillance at each level of the health system for monthly bulletins produced out of those expected", "ITPINoOfWeeks": "Number of weeks or months (Denominator)", "ProportionOfCriteria": "Proportion of criteria which are met by current malaria surveillance strategies (elimination)", "ProportionOfCriteriaBurdenReduction": "Proportion of criteria which are met by current malaria surveillance strategies (Burden Reduction)", "FrequencyOfMonitoring": "Frequency of weekly/monthly malaria epidemic monitoring graphs", "MonthlyNumerator": "No. of monthly epidemic monitoring graphs (Numerator) ", "MonthlyDenominator": "All months (Denominator)", "WeeklyNumerator": "No. of weekly epidemic monitoring graphs (Numerator) ", "WeeklyDenominator": "All weeks (Denominator)", "MetNotMetTooltip": "Routine outputs produced  is considered:<br/>  Met - if all of the below are met (monthly bulletins, epidemic monitoring graphs and annual report)<br/>   Partially met - if at least one of the below is met or partially met( (monthly bulletins, epidemic monitoring graphs and annual report)<br/>   Not met - if none of the below are not met  (monthly bulletins, epidemic monitoring graphs and annual report)<br/>  <br/>   Proportion of monthly bulletins produced is considered;<br/>  Met = 100% <br/>   Partially met  50-99%<br/>   Not met  <50%<br/>   <br/>   Proportion of weekly/monthly malaria epidemic monitoring graphs produced is considered;<br/>   Met = 100% <br/>   Partially met  50-99%<br/>   Not met  <50%<br/>   <br/>  Annual surveillance report produced in the last 12 months is considered:<br/>  Met if a report has been produced (√)<br/>  Not met if no report has been produced (x)", "IPTPResponseDesc": "Calculate the following proportions for IPTp surveillance at each level of the health system for monthly bulletins produced out of those expected", "LarvalResponseDesc": "Calculate the following proportions for larval source management surveillance at each level of the health system for monthly bulletins produced out of those expected", "CommodityResponseDesc": "Calculate the following proportions for commodity tracking data at each level of the health system for monthly bulletins produced out of those expected", "ITNMassResponseDesc": "Calculate the following proportions for ITN mass campaign surveillance at each level of the health system for monthly bulletins produced out of those expected", "ITNResponseDesc": "Calculate the following proportions at each level of the health system for monthly bulletins produced out of those expected", "SMCResponseDesc": "Calculate the following proportions for SMC surveillance at each level of the health system for monthly bulletins produced out of those expected", "EntomologyResponseDesc": "Calculate the following proportions for entomology surveillance at each level of the health system for monthly bulletins produced out of those expected", "DrugEfficacyResponseDesc": "Calculate the following proportions for drug efficacy surveillance at each level of the health system for monthly bulletins produced out of those expected", "IRSResponseDesc": "Calculate the following proportions for IRS surveillance at each level of the health system for monthly bulletins produced out of those expected", "GenomicResponseDesc": "Calculate the following proportions for genomic surveillance at each level of the health system for monthly bulletins produced out of those expected", "ResponseDescription": "Calculate the following proportions at each level of the health system for monthly bulletins produced out of those expected"}, "Indicator_1_3_5": {"ResponseDesc": "Determine from reports or from analysis of surveillance data the proportion of confirmed cases that were investigated and classified correctly in the last year.  Also record examples or list action/ follow-up made from investigations in the last year. If more than one time point is available consider assessing these indicators for the last  year and plot a time trend by month. ", "SectionShouldBeCompleted": "This section should be completed to complete the assessment of this indicator.", "SectionShouldBeCompletedAssessment": "The case classification rate and evidence of follow up/use should be completed to complete the assessment of this indicator.", "NoOfConfirmedCases": "No. of confirmed cases classified correctly(Numerator)", "NoOfInvestigatingCases": "No. of investigated cases (Denominator)", "EvidenceOfFollowUp": "Evidence of follow-up/ use", "ClassClassificationRate": "Case classification rate <br/> (last 12 months/previous calendar year)", "TextboxLabel": "e.g. active case detection was conducted for X% of local cases", "IndicateRate": "Indicate the case classification rate", "Month": "Month", "DataForCaseClassification": "Figure 1.3.5 Case classification rate", "MetNotMetTooltip": "Case classification rate in the past 12 months is considered;<br/> Met  >=80%<br/> Partially met  50-79%<br/> Not met  <50%", "YearOfData": "Year of Data", "InvestigatingCasesInfo": "Cases are considered investigated if the collection of adequate information is sufficient to allow case classification."}, "Indicator_1_3_6": {"ResponseDesc": "Report the proportion of foci that were classified in the previous 12 months. Also record examples or list action/ follow-up made from investigations in the previous 12 months. If more than one time point is available consider assessing this indicator for the last year and plot a time trend by month.", "FociClassificationRate": "Foci classification rate (last 12 months/previous calendar year)", "NoOfFociCaseNumerator": "No. of foci classified (Numerator)", "NoOfFociCaseDenominator": "No. of foci investigated (Denominator)", "FociClassification": "Foci classification rate (last 12 months/previous calendar year)", "IndicateTheFoci": "Indicate the foci classification rate", "Month": "Month", "FociRate": "Rate", "DataForFociClassification": "Figure 1.3.6 Foci classification rate", "MetNotMetTooltip": "Foci classification rate in the past 12 months is considered;<br/> Met  >=80%<br/>  Partially met  50-79%<br/> Not met  <50%", "YearOfData": "Year of Data", "FociInvestigation": "Foci investigation carried out"}, "Indicator_1_3_7": {"ResponseDesc": "In your opinion, what are the challenges for using malaria surveillance data for decision making? <br/> Consider: <br />The data required is not available <br />Poor data quality/ Data are not trusted <br />Surveillance staff are not trained to interpret and use data <br />Other reason(s)", "ITPIResponseDesc": "In your opinion, what are the challenges in the use of IPTi data for decision-making? <br/> Consider: <br/> The data required is not available <br/> Poor data quality/ Data are not trusted <br/> Surveillance staff are not trained to interpret and use data <br/> Other reason(s)", "ITPIProvideDetails": "Provide Details", "IRSResponseDesc": "In your opinion, what are the challenges in the use of IRS data for decision-making?", "ResponseDescDetails": "Consider: <br />The data required is not available <br />Poor data quality/ Data are not trusted <br />Surveillance staff are not trained to interpret and use data <br />Other reason(s)", "EntoResponseDesc": "In your opinion, what are the challenges in the use of entomological data for decision-making? <br/> Consider: <br/> The data required is not available <br/> Poor data quality/ Data are not trusted <br/> Surveillance staff are not trained to interpret and use data <br/> Other reason(s)", "IptpResponseDesc": "In your opinion, what are the challenges in the use of IPTp data for decision-making? <br/> Consider: <br />The data required is not available <br/> Poor data quality/ Data are not trusted <br />Surveillance staff are not trained to interpret and use data <br />Other reason(s)", "CommodityResponseDesc": "In your opinion, what are the challenges in the use of commodity tracking data for decision-making? <br/> Consider: <br/> The data required is not available <br />Poor data quality/ Data are not trusted <br />Surveillance staff are not trained to interpret and use data <br />Other reason(s)", "DrugEfficacyResponseDesc": "In your opinion, what are the challenges in the use of drug efficacy data for decision-making? <br/> Consider: <br/> The data required is not available <br/> Poor data quality/ Data are not trusted <br/> Surveillance staff are not trained to interpret and use data <br/> Other reason(s)", "LarvalResponseDesc": "In your opinion, what are the challenges in the use of larval source management data for decision-making?", "ITNRoutineResponseDesc": "In your opinion, what are the challenges in the use of ITN data for decision-making? <br/> Consider: <br/> The data required is not available <br/> Poor data quality/Data are not trusted <br/> Surveillance staff are not trained  to interpret and use data <br/> Other reason(s)", "SMCResponseDesc": "In your opinion, what are the challenges in the use of SMC data for decision-making? <br/> Consider: <br />The data required is not available <br />Poor data quality/ Data are not trusted <br />Surveillance staff are not trained to interpret and use data <br />Other reason(s) ", "GenomicResponseDesc": "In your opinion, what are the challenges in the use of genomic surveillance data for decision-making? <br/> Consider: <br/> The data required is not available <br/> Poor data quality/ Data are not trusted <br/> Surveillance staff are not trained to interpret and use data <br/> Other reason(s)", "LMSResponseDesc": "In your opinion, what are the challenges in the use of larval source management data for decision-making? <br/> Consider: <br/> The data required is not available <br/> Poor data quality/ Data are not trusted <br/> Surveillance staff are not trained to interpret and use data <br/> Other reason(s)"}}, "DRObjective_2_Responses": {"Indicator_2_1_1": {"RadioAction": "Is malaria a mandatorily notifiable disease ?", "MetNotMetTooltip": "Malaria is a mandatorily notifiable disease is considered; <br/> Met = Yes (√) <br/> Not met= No (x)"}, "Indicator_2_1_2": {"ResponseDesc": "Identify which sectors are involved in reporting malaria cases", "ResponseError": "Reporting malaria cases of at least one health sector should be set to 'Yes' to finalize the indicator", "NoteToCompleteTheAssessment": "At least one box in each of the columns should be selected  to complete the assessment of this indicator.", "HealthSectors": "Health Sectors", "ReportingMalariaCases": "Reporting malaria cases", "MandatedToReport": "Mandated to report", "CaseBasedDataReported": "Case based data reported", "National": "Regional", "Subnational": "District", "ServiceDelivery": "Service Delivery", "DetailsOnNotificationProcesses": "Details on notification processes/ challenges", "SectorsMandatedToreport": "All sectors mandated to report", "AllSectorReportCaseBasedData": "All sectors report case-based data", "NationalTextBoxPlaceHolder": "e.g. 10 central hospitals", "SubNationalTextBoxPlaceHolder": "e.g. 50 regional hospitals", "ServiceDeliveryTextBoxPlaceHolder": "e.g. 1000 health facilities", "CommunityNationalTextBoxPlaceHolder": "e.g. allocates resources", "CommunitySubNationalTextBoxPlaceHolder": "e.g. supervises and trains", "CommunityServiceDeliveryTextBoxPlaceHolder": "e.g. 1700 community health workers", "MetNotMetTooltip": "All sectors are mandated to report is considered; <br/> Met - if all sectors (Public, Private-formal, Private-informal and Community) have a response of Yes ( √ ) <br/> Partially met - if some sectors (Public, Private-formal, Private-informal and Community) have a response of Yes ( √ ) <br/> Not met - if all sectors (Public, Private-formal, Private-informal and Community) have a response of no (x)", "AssessChildInformationDialogTitle": "Assess Indicator 2.1.2", "AssessChildInformationDialogContent": "Changes in indicator 2.1.2 will affect the data of indicator 2.3.1 Please assess indicator 2.3.1", "EnterNumberPlaceHolder": "Please enter number"}, "Indicator_2_1_3": {"ResponseDesc": "Provide details about the elimination activities in place", "OtherMalariaControlDesc": "Please select which malaria control strategies have surveillance implemented", "OtherMalariaControlTitle": "This should be completed for all strategies to complete the assessment of this indicator.", "InvestigationType": "Investigation Type", "ActivityInPlace": "Activity in place", "DataIntegrated": "Data integrated with routine case notification data", "DataLinkage": "Data linkage (data can be linked from different systems)", "DetailsOfData": "Details on data linkage to the index case", "NationalLevel": "National level of involvement", "SubNationalLevel": "Subnational level of involvement", "ServiceDelivery": "Service delivery level of involvement", "Challenges": "Challenges with reporting/implementation", "CaseInvestigation": "Case investigation", "CaseClassification": "Case classification", "FocusInvestigation": "Focus investigation ", "FocusClassification": "Focus Classification", "ActiveCaseDetection": "Active case detection", "ReactiveDetection": "Reactive case detection", "ProactiveDetection": "Proactive case detection", "MalariaControlStrategy": "Malaria control strategy", "SurveillanceImplemented": "Surveillance implemented", "DataIntegratedSurveillance": "Data integrated with the primary malaria case surveillance system", "MethodOfIntegration": "Method of integration (e.g., data feed between systems, linked systems or the same system)", "DetailsOnDataLinkage": "Details on data linkage to the index case", "ChemopreventionInPregnantWomen": "Chemoprevention:Intermittent preventative treatment of malaria in pregnant women (IPTp) ", "ChemopreventionInInfancy": "Chemoprevention:Intermittent preventative treatment of malaria in infancy (IPTi)", "ChemopreventionSMC": "Chemoprevention:Seasonal malaria chemoprophylaxis (SMC)", "ChemopreventionMDA": "Chemoprevention:Mass drug administration (MDA)", "VectorControlRoutineChannel": "Vector control:Insecticide treated nets (ITNs) distributed through routine channels", "VectorControlsMassCampaigns": "Vector control:Insecticide treated nets (ITNs) distributed through mass campaigns", "VectorControlIRS": "Vector control: Indoor residual spraying (IRS)", "VectorControlLSM": "Vector control: Larval source management", "DrugEfficacy": "Drug efficacy surveillance ", "GenomicSurveillance": "Genomic surveillance (drug resistance and pfhrp 2/3 gene deletions)", "EntomologicalSurveillance": "Entomological surveillance ", "CommodityTracking": "Commodity tracking ", "VitalRegistrationSystem": "Vital registration system ", "LaboratoryData": "Laboratory data", "Other": "Other: specify", "OtherMalariaTableFooterTitle": "Proportion of relevant malaria control strategies which are reporting malaria indicators in to some information/surveillance system ", "OtherMalariaTooltip": "Number of 'Yes' of Surveillance implemented (excluding VRS and lab data)/Number of 'Yes' of Malaria Control in place (excluding VRS and lab data)", "HowIsCaseInvestigation": "e.g how is case investigation linked to the index case (i.e., patient name)", "ReviewData": "e.g review data", "LeadsOnInvestigation": "e.g leads on investigation", "ScreensContacts": "e.g screens contacts", "EliminationActivities": "Elimination Activities", "OtherMalariaControlStrategies": "Other malaria control strategies", "EliminationActivity": "Elimination Activity", "MetNotMetTooltip": "If the proportion of relevant malaria control strategies which are reporting malaria indicators in to some information/surveillance system is 100% = Met <br/> If 1-99% = Partially met <br/> If 0% = Not met", "StrategyInPlace": "Malaria control strategy in place", "ResponseError": "Please select “Yes” or “No” for all malaria control strategies in place and surveillance implemented to finalize the indicator", "ResponseStepAError": "Please select “yes” or “No” for all activities in place and surveillance implemented", "EliminationActivitiesFooterTitle": "Proportion of elimination activities which are reporting malaria indicators in to some information/surveillance system", "EliminationActivitiesTooltip": "Number of 'Yes' of Surveillance implemented /Number of 'Yes' of Activity in Place", "ResponseCommonError": "Please complete mandatory fields in both tabs to finalize the indicator"}, "Indicator_2_1_4": {"ResponseDesc": "Each section should be completed for the indicator to be considered assessed", "ResponseDescTwo": "Provide country specific definitions of the following terms, then determine if the definition is consistent with the glossary of WHO malaria terminology", "ResponseLink": "https://www.who.int/publications/i/item/WHO-HTM-GMP-2016.6", "ResponseError": "Please complete Part A and B to finalize the indicator", "ResponseOtherError": "Please fill out all details to finalize the indicator", "CaseDefinition": "Case Definitions", "CaseClassifications": "Case classifications", "FociClassifications": "Foci classifications", "ActiveCaseDetection": "Active Case Detection", "WHODefinition": "WHO definition", "CountryDefinition": "Country definition", "DefinitionOK": "Definition OK <br/> (Yes/No)", "DetectionByHealth": "Detection by health workers of malaria cases at community and household levels, sometimes in population groups that are considered at high risk. Active case detection can consist of screening for fever followed by parasitological examination of all febrile patients or as parasitological examination of the target population without prior screening for fever.", "TimeframeOk": "Timeframe Ok", "Timeframe": "Timeframe (Hours)", "Notification": "Notification", "CaseInvestigation": "Case investigation", "CaseClassification": "Case classification", "FociInvestigation": "Foci investigation", "Response": "Response", "ProportionOfCriteria": "Proportion of criteria which are met by current malaria surveillance strategies (elimination)", "ListTheMandate": "List the mandated timeframe for notification, case investigation, and focus investigation (e.g. within 24 hours, 3 days, and change to 7 days)", "ListTheMandateBurdenReduction": "List the mandated timeframe for notification (e.g. within 24 hours, 3 days, and change to 7 days)", "Local": "Local", "LocalLabelTwo": "A case acquired locally by mosquito-borne transmission", "Indigenous": "Indigenous", "IndigenousLabelTwo": "Any case contracted locally, with no strong evidence of a direct link to an imported case", "Introduced": "Introduced", "IntroducedLabelTwo": "A case contracted locally, with strong epidemiological evidence linking it directly to a known imported case (first-generation local transmission)", "Imported": "Imported", "ImportedLabelTwo": "Malaria case or infection in which the infection was acquired outside the area in which it is diagnosed", "Induced": "Induced", "InducedLabelTwo": "A case the origin of which can be traced to a blood transfusion or other form of parenteral inoculation of the parasite but not to transmission by a natural mosquito-borne inoculation", "Recrudescent": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "RecrudescentLabelTwo": "Malaria case attributed to the recurrence of asexual parasitemia after antimalarial treatment, due to incomplete clearance of asexual parasitemia of the same genotype(s) that caused the original illness. A recrudescent case must be distinguished from reinfection and relapse, in case of P. vivax and P. ovale.", "Relapsing": "Relapsing", "RelapsingLabelTwo": "Malaria case attributed to activation of hypnozoites of P. vivax or P. ovale acquired previously ", "Recurrent": "Recurrent", "RecurrentLabelTwo": "Re-appearance of asexual parasitemia after treatment, due to recrudescence, relapse (in P. vivax and P. ovale infections only) or a new infection. Note that for some cases it may be difficult to distinguish between a recrudescence, relapse or a new infection without molecular methods.", "SuspectedCase": "Suspected Case", "PresumedCase": "Presumed Case", "ConfirmedCase": "Confirmed Case", "SuspectedCaseLabelTwo": "Illness suspected by a health worker to be due to malaria, generally on the basis of the presence of fever with or without other symptoms", "PresumedCaseLabelTwo": "Case suspected of being malaria that is not confirmed by a diagnostic test ", "ConfirmedCaseLabelTwo": "Malaria cases in which the parasite has been detected in a diagnostic test i.e microscopy, RDT or molecular diagnostic test", "ProportionOfCriteriaBurden": "Proportion of Criteria met by current malaria surveillance strategies ( Burden Reduction)", "Active": "Active", "ResidualNonActive": "Residual non-active", "Cleared": "Cleared", "ActiveLabelTwo": "Areas with on-going transmission", "ResidualNonActiveLabelTwo": "Areas with recent local transmission within the previous 3 seasons ", "ClearedLabelTwo": "Areas with no local transmission within the previous 3 seasons", "IptpLabel": "IPTp", "IptpPlaceholderFirstRow": "A full therapeutic course of antimalarial medicine given to pregnant women at routine prenatal visits, regardless of whether the woman is infected with malaria.", "IptpDoseLabel": "At least 3 doses of IPTp received", "IptpPlaceholderSecondRow": "In areas of moderate-to-high malaria transmission of Africa, IPTp-SP be given to all pregnant women at each scheduled antenatal care visit, starting as early as possible in the second trimester, provided that the doses of SP are given at least 1 month apart. The objective is to ensure that at least three doses are received.", "IptpFirstDescription": "This section should be completed to complete the assessment of this indicator.", "IptpSecondDescription": "Provide country specific definitions for the following terms, then determine if the definition is consistent with the definition provided in the glossary of WHO malaria terminology for IPTp surveillance", "SMCResponseDesc": "Provide country specific definitions of the following terms, then determine if the definition is consistent with the glossary of WHO malaria terminology for SMC surveillance", "SMCResponseDescLink": "https://www.who.int/publications/i/item/WHO-HTM-GMP-2016.6", "SMCDefinition1": "SMC is defined as the intermittent administration of full treatment courses of an antimalarial medicine during the malaria season to prevent illness, with the objective of maintaining therapeutic antimalarial drug concentrations in the blood throughout the period of greatest risk.", "SMCDefinition2": "SMC should not be given to children with severe acute illness or who are unable to take oral medication, or to HIV-positive children receiving co-trimoxazole, or children who have received a dose of either amodiaquine or SP during the past month or children with allergy to either drug", "IPTIfullTherapeutic": "A full therapeutic course of sulfadoxine-pyrimethamine delivered to infants in co-administration with DTP2/Penta2, DTP3/Penta3 and measles immunization, regardless of whether the infant is infected with malaria. ", "IPTIWHOEncourages": "WHO encourages co-administration of SP-IPTi in areas with moderate-to-high malaria transmission (>250 cases per 1000 population and a prevalence of P. falciparum/P. vivax >10%) of Africa. IPTi has been shown to be efficacious where parasite resistance to SP, defined as a prevalence of the Pfdhps 540 mutation is ≤ 50%This consists of co-administration of a full therapeutic course of SP with the second and third vaccinations against DTP and vaccination against measles delivered routinely in the Expanded Programme on Immunization —usually at 10 weeks, 14 weeks and about 9 months of age, respectively—to infants at risk for malaria", "IPTIconfirmedCase": "SP-IPTi should not be given to infants receiving a sulfa-based medication for treatment or prophylaxis, including co-trimoxazole (trimethoprim-sulfamethoxazole) which is widely used as prophylaxis against opportunistic infections in HIVinfected infants.", "ITPIconfirmedCase": "IPTi should not be used in target areas for SMC.", "ITNRoutineinsecticideTreatedLabelOne": "Insecticide treated nets (ITNs)", "ITNRoutineinsecticideTreatedLabelTwo": "Mosquito net that repels, disables or kills mosquitoes that come into contact with the insecticide on the netting material. The two categories of insecticide-treated net are:<br/> • conventionally treated net: a mosquito net that has been treated by dipping it into a WHO-recommended insecticide. To ensure its continued insecticidal effect, the net should be re-treated periodically.<br/>• long-lasting insecticidal net: a factory-treated mosquito net made of netting material with insecticide incorporated within or bound around the fibres. The net must retain its effective biological activity for at least 20 WHO standard washes under laboratory conditions and 3 years of recommended use under field conditions. ", "ITNRoutineConventionally": "• conventionally treated net: a mosquito net that has been treated by dipping it into a WHO-recommended insecticide. To ensure its continued insecticidal effect, the net should be re-treated periodically.", "ITNRoutineLongLasting": "• long-lasting insecticidal net: a factory-treated mosquito net made of netting material with insecticide incorporated within or bound around the fibres. The net must retain its effective biological activity for at least 20 WHO standard washes under laboratory conditions and 3 years of recommended use under field conditions. ", "ITNRoutinelongLastingLableOne": "Long lasting insecticidal net", "ITNRoutinelongLastingLableTwo": "A factory-treated mosquito net made of material into which insecticide is incorporated or bound around the fibres. The net must retain its effective biological activity for at least 20 WHO standard washes under laboratory conditions and 3 years of recommended use under field conditions.", "ITNRoutineinsecticideDistributedLabelOne": "Insecticide treated nets (ITNs) distributed through routine channels", "ITNRoutineinsecticideDistributedLabelTwo": "Continuous distribution through ANC and EPI channels should remain functional before, during and after mass distribution campaigns.", "MDADefinition1": "Administration of antimalarial treatment to all age groups of a defined population or every person living in a defined geographical area (except those for whom the medicine is contraindicated) at approximately the same time and often at repeated intervals ", "MDADefinition2": "WHO recommends that a medicine different from that used for first line treatment be used for MDA. Programmes should include monitoring of efficacy, safety and the potential emergence of resistance to the antimalarial medicines deployed for MDA", "MDADefinition3": "Any untoward medical occurrence that may occur during treatment with a pharmaceutical product that is not necessarily causally associated with the treatment", "MDADefinition4": "A response to a drug that is noxious and unintended and that occurs at doses normally used in humans for prophylaxis, diagnosis or therapy of disease or for modifying physiological function", "MDADefinition5": "Any untoward medical occurrence that, at any dose: <br/> • results in death, <br/> • is life threatening, <br/> • requires hospitalization or prolongation of hospitalization, <br/> • results in persistent of significant disability or incapacity or <br/> • results in congenital abnormality or birth defect", "MDAAdverseEvent": "Adverse event", "MDAAdverseDrugReaction": "Adverse drug reaction", "MDASeriousAdverseEvent": "Serious adverse event or reaction", "GenomicResponseDes": "Provide country specific definitions for the following terms, then determine if the definition is consistent with the definition provided in the glossary of WHO malaria terminology for Genomic surveillance", "ITNMassNumberOfHouseholdsLabelOne": "Number of households with at least one ITN for every two people", "ITNMassNumberOfHouseholdsLabelTwo": "Mass campaigns should distribute one ITN for every two persons at risk of malaria. However, for procurement purposes, the calculation to determine the number of ITNs required needs to be adjusted at the population level, since many households have an odd number of members. Therefore, a ratio of one ITN for every 1.8 persons in the target population should be used to estimate ITN requirements, unless data to inform a different quantification ratio are available.", "IRSDefinition1": "Operational procedure and strategy for malaria vector control involving spraying interior surfaces of dwellings with a residual insecticide to kill or repel endophilic mosquitoes", "IRSDefinition2": "Repetition of spraying operations at regular intervals, often designated in terms of the interval between repetitions, e.g. a 6-month spraying cycle when spraying is repeated after a 6-month interval", "IRSDefinition3": "Spray coverage by indoor residual spraying and/or space spraying of houses or habitats in a limited geographical area", "IRSDefinition4": "Spraying the interior walls and ceilings of dwellings with a residual insecticide to kill or repel endophilic mosquito vectors of malaria", "IRSDefinition5": "WHO recommends IRS using a product prequalified by WHO for the prevention and control of malaria in children and adults living in areas with ongoing malaria transmission or with malariogenic potential.", "IRSSprayingCycle": "Spraying cycle", "IRSFocalSpraying": "Focal spraying", "IRSResidualSpraying": "Residual spraying", "EndoInsecticideResistence": "Insecticide resistence", "EndoInsecticideResistenceLabel": "Chemical product (natural or synthetic) that kills insects. Ovicides kill eggs; larvicides (larvacides) kill larvae; pupacides kill pupae;adulticides kill adult mosquitoes. Residual insecticides remain active for an extended period ", "EndoEndophily": "Endophily", "EndoEndophilyLabel": "Proportion of adult anopheles female mosquitoes collected resting indoors in structures sampled. Proportion of adult anopheles female mosquitoes collected resting outdoors in structures sampled, usually per human–hour", "EndoEndophagy": "Endophagy", "EndoEndophagyLabel": "Endophagy= Proportion of adult anopheles female mosquitoes which attempted to bite or successful blood-feed indoors. Exophagy= Proportion of adult anopheles female mosquitoes which attempted to bite or successful blood-feed outdoors", "LarvalDefinition1": "Management of aquatic habitats (water bodies) that are potential habitats for mosquito larvae, in order to prevent completion of development of the immature stages", "LarvalDefinition2": "WHO conditionally recommends the regular application of biological or chemical insecticides to water bodies (larviciding) for the prevention and control of malaria in children and adults living in areas with ongoing malaria transmission or with malariogenic potential as a supplementary intervention in areas where optimal coverage with ITNs or IRS has been achieved, where aquatic habitats are few, fixed and findable, and where its application is both feasible and cost-effective. ", "LarvalDefinition3": " Larviciding should never be seen as a substitute for ITNs or IRS in areas with significant malaria risk. ", "DrugEfficacy": "Drug efficacy", "DrugEfficacyDesc": "Capacity of an antimalarial medicine to achieve the therapeutic objective when administered at a recommended dose, which is well tolerated and has minimal toxicity", "DrugAssistance": "Drug resistance", "DrugAssistanceDesc": "The ability of a parasite strain to survive and/or multiply despite the absorption of a medicine given in doses equal to or higher than those usually recommended", "DrugInclusionCriteria": "Inclusion criteria: <br/> High transmission: Patients with fever, aged 6–59 months and 2000–200 000 asexual parasites/µL. <br/> Moderate transmission:  Patients with fever or a history of fever, children ≤ 12 years and 1000–100 000 asexual parasites/µL. <br/> Low transmission:  Patients with fever or a history of fever, all age groups and ≥ 250 or 500 asexual parasites/µL <br/> Very low transmission: Patients with fever or a history of fever, all age groups and any parasitaemia", "MetNotMetTooltip": "If the proportion of criteria met is 100% = Met <br/> 50% = Partially met <br/>  0% = Not met", "EliminationMetNotMetTooltip": "If the proportion of criteria met is  <br/> 100% = Met <br/> 1% - 99% = Partially met <br/> 0% = Not met", "IRSResponseDesc": "Provide country specific definitions of the following terms, then determine if the definition is consistent with the glossary of WHO malaria terminology for IRS surveillance", "MDAResponseDesc": "Provide country specific definitions of the following terms, then determine if the definition is consistent with the glossary of WHO malaria terminology for MDA surveillance", "ITNResponseDesc": "Provide country specific definitions of the following terms, then determine if the definition is consistent with the glossary of WHO malaria terminology for ITNs surveillance", "DrugEfficacyResponseDesc": "Provide country specific definitions of the following terms, then determine if the definition is consistent with the glossary of WHO malaria terminology for drug efficacy surveillance", "ENTOResponseDesc": "Provide country specific definitions of the following terms, then determine if the definition is consistent with the glossary of WHO malaria terminology for entomology surveillance", "IPTIResponseDesc": "Provide country specific definitions of the following terms, then determine if the definition is consistent with the glossary of WHO malaria terminology for IPTi surveillance", "ITNMassResponseDesc": "Provide country specific definitions of the following terms, then determine if the definition is consistent with the glossary of WHO malaria terminology for ITN mass campaign surveillance", "LarvalResponseDesc": "Provide country specific definitions of the following terms, then determine if the definition is consistent with the glossary of WHO malaria terminology for larval source management surveillance", "CommodityResponseDesc": "Provide country specific definitions of the following terms, then determine if the definition is consistent with the glossary of WHO malaria terminology for commodity tracking surveillance", "ProportionCriteriaMet": "Proportion of criteria met", "IndicatorResponseDesc": "Provide country specific definitions for the following terms, then determine if the definition is consistent with the definition provided in the glossary of WHO malaria terminology", "IPTiLableOne": "IPTi", "WHORecommendation": "WHO recommends", "ContaIndications": "Contra-indications", "IPTiLableFour": "Target areas", "IptiNote": "A full therapeutic course of sulfadoxine-pyrimethamine delivered to infants in co-administration with DTP2/Penta2, DTP3/Penta3 and measles immunization, regardless of whether the infant is infected with malaria", "WHOEncouragement": "The co-administration of SP-IPTi with DTP2, DTP3 and measles immunization to infants, through routine EPI in countries in sub-Saharan Africa, in areas: <br/>• with moderate-to-high malaria transmission (Annual Entomological Inoculation Rates ≥10), and <br/>  • where parasite resistance to SP is not high – defined as a prevalence of the pfdhps 540 mutation of ≤ 50%.", "WHOInfants": "SP-IPTi should not be given to infants receiving a sulfa-based medication for treatment or prophylaxis, including co-trimoxazole (trimethoprim-sulfamethoxazole) which is widely used as prophylaxis against opportunistic infections in HIVinfected infants.", "IPTiAgainstSMC": "IPTi should not be used in target areas for SMC.", "SMCTitle": "SMC", "MDADefination": "MDA", "InclusionCriteria": "Inclusion criteria"}, "Indicator_2_2_1": {"ResponseDesc": "What information systems (i.e. platforms or storage systems; these may be electronic or paper) are used for collecting malaria case surveillance data ? List name(s) in the table columns.Consider all systems used for reporting malaria cases e.g  IDSR,  HMIS, DHIS2 and/or a separate case-based system. Consider both aggregate and case-based systems. Describe the details of each system.", "ResponseTitle": " Details for information system 1 should be completed to complete the assessment of this indicator.", "ResponseError": "Details for at least one information system in this section should be completed to complete the assessment of this indicator.", "InformationSystem1": "Information system 1 ", "InformationSystem1Desc": "this should be the primary commodity database e.g. DHIS2", "CaseInformationSystem1Desc": "this should be the primary case surveillance database e.g. DHIS2", "IptiInformationSystem1Desc": "this should be the primary IPTi database e.g. DHIS2", "IptpInformationSystem1Desc": "this should be the primary IPTp database e.g. DHIS2", "SMCInformationSystem1Desc": "this should be the primary SMC database e.g. DHIS2", "MDAInformationSystem1Desc": "this should be the primary MDA database e.g. DHIS2", "ITNInformationSystem1Desc": "this should be the primary ITN Routine database e.g. DHIS2", "IRSInformationSystem1Desc": "this should be the primary IRS database e.g. DHIS2", "LMSInformationSystem1Desc": "this should be the primary larval source management database e.g. DHIS2", "EntoInformationSystem1Desc": "this should be the primary entomology database e.g. DHIS2", "DrugInformationSystem1Desc": "this should be the primary drug efficacy database e.g. DHIS2", "GenomicsInformationSystem1Desc": "this should be the primary genomics database e.g. DHIS2", "ITNMassInformationSystem1Desc": "this should be the primary ITN mass campaign database e.g. DHIS2", "InformationSystem2": "Information system 2", "InformationSystem3": "Information system 3", "InformationSystem4": "Information system 4", "PlatformName": "Platform Name", "PlatformNameFirstPlaceholder": "e.g. DHIS2", "DataStorage": "Data storage software", "DataStorageFirstPlaceholder": "e.g. access database", "DataStorageSecondPlaceHolder": "e.g.postgreSQL", "Database": "Database front-end (how do you interact with the database: access, modify, delete)", "DatabaseFirstPlaceholder": "e.g. custom software", "DatabaseSecondPlaceHolder": "e.g DHIS2", "Version": "Version ", "VersionFirstPlaceholder": "e.g. software version 10", "VersionSecondPlaceHolder": "e.g. 2.34", "Instance": "Instance(s)", "InstanceFirstPlaceholder": "e.g. multiple instances for DHIS2", "InstanceSecondPlaceHolder": " e.g. 1 aggregate, 1 case-based", "ManagedBy": "Managed/ hosted by", "ManagedByFirstPlaceholder": "e.g. MoH", "ManagedBySecondPlaceHolder": "e.g. cloud hosting", "StartDate": "Start date + no. of full years of data in the system", "StartDateFirstPlaceholder": "e.g. 20th January 2018 (2.5 years)", "StartDateSecondPlaceHolder": "e.g. May 2020 (1 year)", "HealthSectors": "Health sectors covered", "HealthSectorsFirstPlaceholder": "e.g. public", "HealthSectorsSecondPlaceHolder": "e.g. public, private sector data", "GeographicalScope": "Geographical scope", "GeographicalScopeFirstPlaceholder": "e.g. 80% of all districts", "TypeOfSurveillance": "Type of surveillance", "TypeOfSurveillanceFirstPlaceholder": "e.g. aggregate reporting passive/ active detection", "TypeOfSurveillanceSecondPlaceHolder": "e.g. aggregate and case-based", "LevelOfReporting": "Level of reporting", "LevelOfReportingFirstPlaceholder": "e.g. by facility", "LevelOfReportingSecondPlaceHolder": " e.g. health facility", "OtherDiseases": "Other diseases included ", "OtherDiseasesFirstPlaceholder": "yes/ no (name which ones)", "OtherDiseasesSecondPlaceHolder": "yes/ no (name which ones)", "CurrentStatus": "Current status (ongoing, about to scale-up, implemented, abandoned)", "CurrentStatusFirstPlaceholder": " e.g. ongoing", "CurrentStatusSecondPlaceHolder": "e.g. ongoing", "AdministrativeUnit": "Administrative unit shapefiles (provinces, districts, villages, etc.)", "AdministrativeUnitFirstPlaceholder": "e.g yes provincial level", "AdministrativeUnitSecondPlaceHolder": "e.g yes district level", "MasterFacility": "Master Facility List", "MasterFacilityFirstPlaceholder": "e.g yes", "MasterFacilitySecondPlaceHolder": "e.g yes public and private", "IptpResponseDescFirst": "What information systems (i.e. platforms or storage systems; these may be electronic or paper) are used for collecting IPTp  data ?", "IptpResponseDescSecond": "Consider all systems used for reporting IPTp.<br/>Consider both aggregate and case based systems,electronic and paper.", "IptpResponseDescThird": "Note that if IPTp is included in the primary case surveillance system then this indicator does not need to be assessed.", "ITPIResponseDesc": "What information systems (i.e. platforms or storage systems; these may be electronic or paper) are used for collecting IPTi data?", "ITPIResponseTitle": "Consider all systems used for reporting malaria cases e.g  IDSR,  HMIS, DHIS2 and/or a separate case-based system. Consider both aggregate and case-based systems. Describe the details of each system.Note that if IPTi is included in the primary case surveillance system then this indicator does not need to be assessed.", "ITPIDatabase": "Database front-end (how do you interact with the database: access, modify, delete)", "SMCResponseDesc": "What information systems (i.e. platforms or storage systems; these may be electronic or paper) are used for collecting SMC data?", "SMCResponseNote": "Consider all systems used for reporting SMC.", "ITNRoutineResponseDesc": "What information systems (i.e. platforms or storage systems; these may be electronic or paper) are used for collecting ITN data ?", "ITNRoutineResponseTitle": "Consider all systems used for reporting ITN <br/> Consider both aggregate and case based systems,electronic and paper.<br/><br/><i> Note that if ITN is included in the primary case surveillance system then this indicator does not need to be assessed.<i/>", "MDAResponseDesc": "What information systems (i.e. platforms or storage systems; these may be electronic or paper) are used for collecting MDA data?", "MDAResponseNote": "Consider all systems used for reporting MDA <br/>Consider both aggregate and case based systems, electronic and paper", "IRSResponseDesc": "What information systems (i.e. platforms or storage systems; these may be electronic or paper) are used for collecting IRS data ?", "EndoResponseDesc": "What information systems (i.e. platforms or storage systems; these may be electronic or paper) are used for collecting entomology data?", "LarvalResponseDesc": "What information systems (i.e. platforms or storage systems; these may be electronic or paper) are used for collecting larval source management data?", "MetNotMetTooltip": "The assessment of this indicator is subjective and the outcome should be based on expert opinion as to whether existing systems are adequate for data capture and that there is adequate integration between them/systems are not redundant (multiple systems collecting the same information). The ideal situation would be to have one integrated information system. In general the indicator can be considered; <br/> Met = No changes to current systems or integration/data linkage <br/> Partially met = Minor changes to current systems or integration/data linkage <br/> Not met = Major changes to current systems or integration/data linkage", "GenomicResponseDesc": "What information systems (i.e. platforms or storage systems; these may be electronic or paper) are used for collecting genomic data?", "DrugEfficacyResponseDesc": "What information systems (i.e. platforms or storage systems; these may be electronic or paper) are used for collecting drug efficacy data?<br/></br> Consider all systems used for reporting drug efficacy.<br/>Consider both aggregate and case-based systems,electronic and paper.", "ITNMassResponseDesc": "What information systems (i.e. platforms or storage systems; these may be electronic or paper) are used for collecting ITN mass campaign data ?", "CommodityResponseDesc": "What information systems (i.e. platforms or storage systems; these may be electronic or paper) are used for collecting commodity tracking data ?<br/><br/> Consider all systems used for reporting commodity tracking.<br/>Consider both aggregate and case-based systems,electronic and paper.", "ITPIResponseAssessed": "Details for at least one information system should be completed to complete the assessment of this indicator.", "ResponseNote": "Details for at least one information system should be completed to complete the assessment of this indicator. <br/> List name(s) in the table columns.", "ResponseList": "List name(s) in the table columns.", "ResponseDescription": "Consider both aggregate and case based systems,electronic and paper.", "NoteForCompleteAssessment": "Please add this information to the main information systems diagram that you have developed for sub-objective 2.2.", "CommodityNote": "Note that if commodity tracking is included in the primary case surveillance system then this indicator does not need to be assessed.", "DrugEfficacyNote": "Note that if drug efficacy is included in the primary case surveillance system then this indicator does not need to be assessed.", "EntomologyNote": "Note that entomology is included in the primary case surveillance system then this indicator does not need to be assessed.", "EndoResponseNote": "Consider all systems used for reporting entomology. <br/> Consider both aggregate and case based systems,electronic and paper.", "GeonomicNote": "Note that if genomic is included in the primary case surveillance system then this indicator does not need to be assessed.", "GeonomicResponseNote": "Consider all systems used for reporting genomic.<br/> Consider both aggregate and case based systems,electronic and paper.", "IptiNote": "Note that if IPTi is included in the primary case surveillance system then this indicator does not need to be assessed.", "IptiResponseNote": "Consider all systems used for reporting IPTi.<br/> Consider both aggregate and case based systems,electronic and paper.", "IRSNote": "Note that if IRS is included in the primary case surveillance system then this indicator does not need to be assessed.", "IRSResponseNote": "Consider all systems used for reporting IRS.<br/> Consider both aggregate and case based systems,electronic and paper.", "LarvalNote": "Note that if larval source management is included in the primary case surveillance system then this indicator does not need to be assessed.", "LarvalResponseNote": "Consider all systems used for reporting larval source management.<br/> Consider both aggregate and case based systems,electronic and paper.", "MDAEfficacyNote": "Note that if MDA is included in the primary case surveillance system then this indicator does not need to be assessed.", "SMCNote": "Note that if SMC is included in the primary case surveillance system then this indicator does not need to be assessed.", "InformationSystem1WithDesc": "Information system 1 (this should be the primary case surveillance database e.g. DHIS2)"}, "Indicator_2_2_2": {"ResponseDesc": "Using the questions below, summarize the primary malaria case surveillance system ( as per 2.2.1) considering the four attributes", "Flexibility": "Flexibility", "FlexibilityTabDesc": "The procedure for making revisions is straightforward", "Stability": "Stability", "StabilityTabDesc": "The system is fully operating with a planned and costed maintenance", "Interoperability/integration": "Interoperability/integration", "InteroperabilityTabDesc": "The system uses standard data formats and integrated with other data sources", "VisualizationCapacities": "Visualization capacities", "VisualizationCapacitiesTabDesc": "The system includes functionality for visualizing data in a dashboard ", "IsFlexibility": "Is there flexibility?", "FlexibilityProcess": "Is the process for making revisions documented? Where is this documented? <br/> Is the process for making revisions simple? Describe the process.", "FlexibilityPlaceholder": "consider whether the NMP have editing rights to the system, how do you add indicators, accommodate changes in case definitions or technology, changes in SOPs and recognition of new risk factors etc. ", "IsStability": "Is there stability?", "StabilityProcess": "Is the information system fully operating?<br/>Is there a plan in place for maintenance and troubleshooting of the information system? <br/> Is there a budget for long-term technical support and training? (If available, provide the budget) ", "StabilityPlaceholder": "i.e. able to collect, manage, and export data properly without failure, or 'Are there any documented or reported occasions of system failure?')i.e., consider internet connectivity and speed)", "IsInteroperability": "Is there Interoperability/ integration?", "InteroperabilityProcess": "Does the information system use standard data formats? <br/> Does the  information system integrate data from other sources?", "InteroperabilityPlaceholder": "e.g. in electronic data interchange.Describe the process of data integration from other sources List what data is integrated and the method of integration", "IsVisualizationCapacities": "Is there visualization capacities?", "VisualizationCapacitiesProcess": "Does the information system have a dashboard interface?(ability to filter/visualize summarize data).<br/> Does the Dashboard include mapping capabilities? (can filter/visualize/summarize data in an automated map) <br/> Does the dashboard include data quality indicators? (completeness, timeliness, etc.)", "VisualizationCapacitiesPlaceholder": "OBSERVE: Dashboard is functional (i.e. can you see charts and tables with data) This requires access to, or a demonstration of the dashboard to observe that it works as it should", "InformationSystemsStepBDesc1": "Do you find the information system easy to use? ", "InformationSystemsStepBPlaceholder1": "Consider data entry, navigation, report generation, data visualization, system stability", "InformationSystemsStepBDesc2": "What additional attributes would improve information system?", "InformationSystemsStepBPlaceholder2": "Content included here can be added as narrative in the Technical Brief and/or Report and as information for generating recommendations for surveillance improvements. These details can be used to develop an optional Information Systems Diagram showing what IS are used and how they are integrated ", "InformationSystemsStepBDesc3": "Describe any planned surveillance information system changes and expected dates with regards to integration of other malaria surveillance? ", "InformationSystemsStepBPlaceholder3": "Content included here can be added as narrative in the Technical Brief and/or Report and as information for generating recommendations for surveillance improvements", "ITPIResponseDesc": "List and describe information systems that are used to routinely report IPTi data ", "ITPIResponseTitle": "Note that if IPTi is included in the primary case surveillance system then this indicator does not need to be assessed", "SMCResponseDesc": "Using the questions below, summarize the primary case surveillance system considering four attributes", "SMCResponseNote": "Note that if SMC is included in the primary case surveillance system then this indicator does not need to be assessed", "ITNRoutineResponseDesc": "Using the questions below, summarize the primary case surveillance system considering four attributes", "ITNRoutineResponseTitle": "Note that if ITN is included in the primary case surveillance system then this indicator does not need to be assessed", "GenomicResponseDesc": "Note that if genomic surveillance is included in the primary case surveillance system then this indicator does not need to be assessed", "IRSResponseNote": "Note that if IRS is included in the primary case surveillance system then this indicator does not need to be assessed", "IRSCompleteAllSections": "Note that if IRS is included in the primary case surveillance system then this indicator does not need to be assessed", "EndoResponseDesc": "Using the questions below, summarize the primary malaria case surveillance system ( as per 2.2.1) considering the four attributes", "EndoTitleOne": "Note that if entomology is included in the primary case surveillance system then this indicator does not need to be assessed", "EndoTitleTwo": "User must complete all sections to complete the assessment of this indicator", "EndoPlaceholderOne": "Content included here can be added as narrative in the Technical Brief and/or Report and as information for generating recommendations for surveillance improvements. These details can be used to develop an optional Information Systems Diagram showing what IS are used and how they are integrated ", "EndoTextboxLabel": "Describe any planned surveillance information system changes and expected dates with regards to integration of other malaria surveillance? ", "EndoPlaceholderTwo": "Content included here can be added as narrative in the Technical Brief and/or Report and as information for generating recommendations for surveillance improvements ", "CommodityResponseNote": "Note that if commodity tracking is included in the primary case surveillance system then this indicator does not need to be assessed", "LarvalResponseNote": "Note that if larval source management is included in the primary case surveillance system then this indicator does not need to be assessed", "MetNotMetTooltip": "This indicator is considered; <br/> Met if the response to all 4 attributes is yes (√ ) <br/>  Partially met if the response to at least one attribute is yes (√ ) <br/> Not met if the response to all attricutes is no (x)", "IPTPResponseDesc": "Using the questions below, summarize the primary IPTp surveillance system ( as per 2.2.1) considering the four attributes", "IPTPResponseTitle": "Note that if IPTp is included in the primary case surveillance system then this indicator does not need to be assessed User must complete all sections to complete the assessment of this indicator", "IPTPAssess": "User must complete all sections to complete the assessment of this indicator", "DrugEfficacyResponseDesc": "Note that if drug efficacy is included in the primary case surveillance system then this indicator does not need to be assessed ", "CommodityResponseDesc": "Note that if commodity tracking data is included in the primary case surveillance system then this indicator does not need to be assessed separately for commodity tracking", "CommodityAssessment": "Using the questions below, summarize the primary commodity tracking surveillance system ( as per 2.2.1) considering the four attributes", "DrugEfficacyAssessment": "Using the questions below, summarize the primary drug efficacy surveillance system ( as per 2.2.1) considering the four attributes", "EntoAssessment": "Using the questions below, summarize the primary entomology surveillance system ( as per 2.2.1) considering the four attributes", "GenomicsAssessment": "Using the questions below, summarize the primary genomic surveillance system ( as per 2.2.1) considering the four attributes", "LarvalAssessment": "Using the questions below, summarize the primary larval source management surveillance system ( as per 2.2.1) considering the four attributes", "ITNAssessment": "Using the questions below, summarize the primary ITN surveillance system ( as per 2.2.1) considering the four attributes", "ITNMassAssessment": "Using the questions below, summarize the primary ITN mass campaign  surveillance system ( as per 2.2.1) considering the four attributes", "IPTIMassAssessment": "Using the questions below, summarize the primary IPTi surveillance system ( as per 2.2.1) considering the four attributes", "IRSMassAssessment": "Using the questions below, summarize the primary IRS surveillance system ( as per 2.2.1) considering the four attributes", "SMCMassAssessment": "Using the questions below, summarize the primary SMC surveillance system ( as per 2.2.1) considering the four attributes"}, "Indicator_2_2_3": {"ResponseDesc": "Does a common unique identifier exist which identifies duplicate patients across service delivery points? This should be an identifier that uniquely identifies patients and not the health record e.g a national ID or national health insurance number.", "CommonIdentifierExists": "If a common unique identifier exists, can it be used to link laboratory and case data ?", "MetNotMetTooltip": "Unique identifier exists which identifies duplicate patients -  <br/> This indicator is considered: <br/> Met if the response is yes ( √) <br/> Not met if the response is no (x)"}, "Indicator_2_2_4": {"BothSectionRequired": "Both sections should be completed to complete the assessment of the indicator", "MFLExist": "Does a  Master Facility List exist which has identifiers for individual health facilities? Ideally the type of facility should be indicated (e.g outpatient clinic, hospital) as well as the health sector it belongs to (public or private)", "SameMasterFacility": "Is the same  Master Facility List  used for laboratories and other surveillance systems which collect malaria data?", "MetNotMetTooltip": "A Master Facility List exists which has identifiers for individual health facilities -  <br/> This indicator is considered: <br/> Met if the response is yes ( √) <br/>  Not met if the response is no (x)"}, "Indicator_2_2_5": {"ResponseDesc": "Does the country have an integrated national electronic malaria database which collects information on cases, interventions, commodities and  finances, with subnational disaggregation of data e.g national malaria data repository ?", "MetNotMetTooltip": "The country has an integrated national electronic malaria database which collects information on cases, interventions, commodities and  finances, with subnational disaggregation of data e.g national malaria data repository  - <br/> This indicator is considered:<br/> Met if the response is yes ( √)<br/>  Not met if the response is no (x)"}, "Indicator_2_2_6": {"ResponseDesc": "Is drug efficacy monitoring integrated into case surveillance, with parasitologic testing (at a minimum) on day 3 and 28 (or 42, depending on the type of artemisinin-based combination therapy).", "MetNotMetTooltip": "Drug efficacy monitoring is integrated into case surveillance, with parasitologic testing (at a minimum) on day 3 and 28 (or 42, depending on the type of artemisinin-based combination therapy) -  <br/> This indicator is considered: <br/> Met if the response is yes ( √) <br/>  Not met if the response is no (x)"}, "Indicator_2_3_1": {"ResponseDesc": "Provide details on the guidelines, strategic documents, or operational documents that provides the framework, process, and other details for the data recording, reporting, analysis and use of case surveillance data?", "ResponseError": "At least one guideline must be filled in for this indicator to be assessed to complete the assessment of this indicator", "NoteToCompleteAssessment": "At least one guideline must be filled in for this indicator to be assessed to complete the assessment of this indicator", "ResponseDesc1": "Identify which health sectors that report malaria cases have these guidelines (distributed to each sector). For the public sector please specify whether or not guidelines have been distributed to all health system levels", "GuidelineName": "Guideline Name", "LinkToCopy": "Link to copy", "IsReliableConnection": "If available on the internet is there a reliable connection and the guideline that can be easily accessed at all times", "Guideline": "guideline", "Guideline1": "Surveillance Guideline", "Guideline2": "Guideline 2", "Guideline3": "Guideline 3", "Guideline4": "Guideline 4", "Guideline1Name": "Surveillance Guideline Name", "Guideline2Name": "Guideline 2 Name", "Guideline3Name": "Guideline 3 Name", "Guideline4Name": "Guideline 4 Name", "HealthSectors": "Health sectors", "ReceivedGuidelines": "Received guidelines", "Details": "Details", "TableFooterProportions": "Proportion of sectors that receive guidelines (%)", "TableFooterProportionsTooltip": "Numerator (Number of 'Yes')/Denominator (Number of rows (number of 'Yes' from L11:L26)", "IPTINoteToCompleteAssessment": "At least one guideline must be filled in for this indicator to be assessed to complete the assessment of this indicator.<br/> For each: Use the checklist table below to identify which relevant sectors that report IPTi data have these guidelines (distributed to each sector) (captured in Table 2.1). For the public sector please sepcify whether or not guidelines have been distributed to all health system levels.", "Public": "Public", "PrivateFormal": "Private - formal", "PrivateInformal": "Private - informal", "Community": "Community", "SMCNoteToCompleteAssessment": "At least one guideline must be filled in for this indicator to be assessed to complete the assessment of this indicator <br/> For each: Use the checklist table to the right to identify which relevant sectors that report SMC data have these guidelines (distributed to each sector) (captured in Table 2.1). For the public sector please sepcify whether or not guidelines have been distributed to all health system levels.", "SMCResponseDesc1": "Identify which relevant sectors that report SMC data have these guidelines (distributed to each sector) (captured in Table 2.1). For the public sector please sepcify whether or not guidelines have been distributed to all health system levels.", "GenomicResponseDesc": "Provide details on the guidelines, strategic documents, or operational documents that provides the framework, process, and other details for the data recording, reporting, analysis and use of genomic surveillance data.", "ITNMassResponseDesc": "Provide details on the four guidelines, strategic documents, or operational documents that provides the framework, process, and other details for the data recording, reporting, analysis and use of ITN data", "IRSResponseDesc": "Provide details on the four guidelines, strategic documents, or operational documents that provides the framework, process, and other details for the data recording, reporting, analysis and use of IRS data", "EntoResposeDesc": "Provide details on the guidelines, strategic documents, or operational documents that provides the framework, process, and other details for the data recording, reporting, analysis and use of entomology surveillance data", "CommodityResponseDesc": "Enter details on the guideline, strategic document, or operational document that provides the framework, process, and other details for the data recording, reporting, analysis and use of commodity tracking data", "LarvalResponseDesc": "Provide details on the guidelines, strategic documents, or operational documents that provides the framework, process, and other details for the data recording, reporting, analysis and use of larval source management data", "DrugResponseDesc": "Provide details on the guidelines, strategic documents, or operational documents that provides the framework, process, and other details for the data recording, reporting, analysis and use of drug efficacy data", "ITNRoutineResponseDesc": "Provide details on the guidelines, strategic documents, or operational documents that provides the framework, process, and other details for the data recording, reporting, analysis and use of ITN data ", "TableProportionsTooltip": "<p>Guideline 1 : 'Yes' Indicator 2.3.1 of {Guideline 1}/ <br/> 'Yes' Indicator 2.1.2 of 'Reporting malaria cases'</p> <p>Guideline 2 : 'Yes' Indicator 2.3.1 of {Guideline 2} / <br/> 'Yes' Indicator 2.1.2 of 'Reporting malaria cases'</p><p> Guideline 3 : 'Yes' Indicator 2.3.1 of {Guideline 3} / <br/> 'Yes' Indicator 2.1.2 of 'Reporting malaria cases' </p> Guideline 4 : 'Yes' Indicator 2.3.1 of {Guideline 4} / <br/> 'Yes' Indicator 2.1.2 of 'Reporting malaria cases'", "ResponseOtherError": "Please fill out all details for 'Guideline' to finalize the indicator", "IPTPPublic": "Public", "IPTPResponseDesc": "Provide details on the guidelines, strategic documents, or operational documents that provides the framework, process, and other details for the data recording, reporting, analysis and use of IPTp surveillance data", "IPTPNote": "List name(s) in the table columns", "IPTPNoteToCompleteAssessment": "For each: Use the checklist table below to identify which relevant sectors that report IPTp data have these guidelines (distributed to each sector)(captured in table 2.1) for this public sector this specify whether or not guideline have been distributed to all health level", "SMCResponseDesc": "Provide details on the guidelines, strategic documents, or operational documents that provides the framework, process, and other details for the data recording, reporting, analysis and use of SMC surveillance data", "IPTIResponseDesc": "Provide details on the guidelines, strategic documents, or operational documents that provides the framework, process, and other details for the data recording, reporting, analysis and use of IPTi surveillance data", "BurdenAssessment": "Kindly assess indicator '2.1.2' before filling information for this indicator", "Commodity": "Commodity tracking", "IPTP": "IPTp", "IPTI": "IPTi", "SMC": "SMC", "MDA": "MDA", "ITNRoutine": "ITN", "ITNMassCampaign": "ITN", "IRS": "IRS", "LaravalSource": "Larval source management", "Entomology": "Entomology", "DrugEfficacy": "Drug Efficacy", "Genomics": "Genomic", "ParentDataNotFilledError": "Kindly assess indicator '2.1.2' before finalize information for this indicator", "MetNotMetTooltip": "If proportion of sectors that receive guidelines is  <br/> Met  >=80% <br/> Partially met  50-79% <br/> Not met  <50%"}, "Indicator_2_3_2": {"ResponseTitle": "Determine whether the minimum expected content of malaria surveillance guidelines is present in the country document", "ResponseDesc": " At least one guideline should be assessed to complete the assessment of this indicator.", "Guideline1": "Guideline 1", "Guideline2": "Guideline 2", "Guideline3": "Guideline 3", "Guideline4": "Guideline 4", "MinimumExpectedContent": "Minimum expected content", "Checklist": "Checklist", "Details": "Details", "GuidlineName": "Guideline Name", "PublicationDate": "Publication date (Or date of last revision)", "DocumentWHORecomendation": "Document includes malaria case/data definitions in-line with WHO recommendation ", "ProceduralGuidlines": "Document includes procedural guidelines for data recording, reporting/ transmission, data storage, management, quality assurance, analysis, and dissemination and use of data", "DatSecurityStatement": "Document includes a data security statement (i.e. all service-delivery points are regulated to protect the confidentiality of all surveillance data e.g. records, registers)", "Consequences": "Document includes consequences for non-compliance with surveillance regulations ", "AllAtRiskPopulation": "Document includes that all at-risk populations are covered by passive or proactive surveillance for malaria and high-risk populations are routinely reviewed to ensure they are adequately covered by surveillance", "CaseInvestigation": "Document includes that case investigations are undertaken to determine the likely place of infection. ", "CaseAquiredLocally": "Document includes that if the case was acquired locally, the location of infection (household or place of work) is geolocated", "AdditionalInvestigation": "Document includes that additional investigations are conducted to support the classification of introduced cases when no imported case can be linked directly to the introduced case", "SOPToInvestigation": "Document includes SOPs to investigate and respond to malaria outbreaks are included in the country's outbreak response system", "CountryShouldReport": "Document includes that country should report all malaria cases annually to WHO", "TableFooterOne": "Proportion of expected content included in current surveillance guidelines (elimination)", "TableFooterTwo": "Proportion of expected content included in current surveillance guidelines (burden reduction)"}, "Indicator_2_4_1": {"NationalRequired": "National required", "SubNationalRequired": "Subnational required", "ServiceDeliveryRequired": "Service delivery required", "CurrentlyAvailable": "Currently available", "NationalCurrentlyAvailable": "National currently available", "SubNationalCurrentlyAvailable": "Subnational currently available", "ServiceDeliveryCurrentlyAvailable": "Service delivery currently available", "WhoAndHowManyStaff": "Who and how many staff are required for malaria case surveillance at each level of the health system? ", "HowManyStaff": "How many staff in each role and at each level are currently available? ", "ConsiderAllStaff": "Consider all staff required at all levels of the health system, such as health care workers, malaria surveillance officers, epidemiologists, statisticians, M & E officer, data managers, etc. ", "StaffRole": "Staff role", "IptpWhoAndHowManyStaff": "Who and how many staff are required for surveillance of IPTp data at each level of the health system?", "IptpStaffRole": "List staff by role in the table rows, then identify how many are required and how many are available in the corresponding column.", "SMCResponseDesc": "Who and how many staff are required for  surveillance of SMC data at each level of the health system?", "IRSResponseDesc": "Who and how many staff are required for  surveillance of IRS data at each level of the health system? <br/> How many staff in each role and at each level are currently available ? <br/> List staff by role in the table rows, then identify how many are required and how many are available in the corresponding column.", "ProportionAvailable": "Proportion available", "EntoResponseDesc": "List staff by role in the table rows, then identify how many are required and how many are available in the corresponding column.", "CommodityResponseDesc": "Who and how many staff are required for  surveillance of commodity tracking data at each level of the health system? <br/><br/> How many staff in each role and at each level are currently available? <br/><br/> Consider all staff required at all levels of the health system, such as health care workers, malaria surveillance officers, epidemiologists, statisticians, M & E officer, data managers, etc.<br/><br/>  List staff by role in the table rows, then identify how many are required and how many are available in the corresponding column.", "LarvalResponseDesc": "Who and how many staff are required for surveillance of larval source management data at each level of the health system? <br/> How many staff in each role and at each level are currently available? <br/> List staff by role in the table rows, then identify how many are required and how many are available in the corresponding column.", "DrugResponseDesc": "Who and how many staff are required for surveillance of drug efficacy data at each level of the health system? <br/> How many staff in each role and at each level are currently available? <br/> List staff by role in the table rows, then identify how many are required and how many are available in the corresponding column.", "ITNMassWhoAndHowManyStaff": "Who and how many staff are required for surveillance of ITN data at each level of the health system? ", "EntoWhoAndHowManyStaff": "Who and how many staff are required for surveillance of entomological data at each level of the health system?", "MetNotMetTooltip": "If proportion of available surveillance staff for malaria is <br/> >=80% = Met <br/> 50-79% = Partially met <br/> If < 50% = Not met", "GenomicResponseDesc": "Who and how many staff are required for genomic Surveillance data at each level of the health system? <br/> How many staff in each role and at each level are currently available? <br/> List staff by role in the table rows, then identify how many are required and how many are available in the corresponding column.", "IPTiResponseDesc": "Who and how many staff are required for malaria IPTi surveillance at each level of the health system?"}, "Indicator_2_4_2": {"WhatEquipment": "What equipment and infrastructure is required for malaria case surveillance ?", "HowManyOfEach": "How many of each are required? How many are currently available and functional?", "ConsiderAllEquipmemt": "Consider all equipment needed to do surveillance at each level of the health system, such as data recording and reporting forms/ tools, computers, printer, mobile phones, mobile network connection (voice or SMS), internet connection (mobile or fixed), electricity, etc.", "ListEquipment": "List equipment in the table rows, then identify how many are required and how many are available in the corresponding column.", "DataShouldBeCompleted": "Data should be completed for at least the national level to complete the assessment of this indicator", "Equipment": "Equipment", "SMCResponseDesc": "What equipment and infrastructure is required for  surveillance of SMC data ? <br/> How many of each are required? How many are currently available and functional?", "ITNRoutineWhatEquipment": "What equipment and infrastructure is required for surveillance of ITN data ?", "GenomicResponseDesc": "What equipment and infrastructure is required for genomic surveillance data? <br/> How many of each are required? How many are currently available and functional?", "IRSResponseDesc": "What equipment and infrastructure is required for surveillance of IRS data ? How many of each are required? How many are currently available and functional?", "EntoResponseDesc": "What equipment and infrastructure is required for surveillance of entomological data? ", "CommodityResponseDesc": "What equipment and infrastructure is required for surveillance of commodity tracking data? <br/> How many of each are required? How many are currently available and functional?", "LarvalResponseDesc": "What equipment and infrastructure is required for surveillance of larval source management data ?", "DrugResponseDesc": "What equipment and infrastructure is required for surveillance of drug efficacy data ? <br/> How many of each are required? How many are currently available and functional? ", "MetNotMetTooltip": "If proportion of equipment available is <br/> >=80% = Met <br/> 50-79% = Partially met <br/> If < 50% = Not met", "IPTpResponseDesc": "What equipment and infrastructure is required for IPTp surveillance?", "IPTiResponseDesc": "What equipment and infrastructure is required for IPTi surveillance?"}, "Indicator_2_4_4": {"ResponseDesc": "All sections should be completed to complete the assessment of this indicator", "PlannedAllocation": "Describe the planned allocation or procedure for continuous availability of these resources listed in 2.4.2 at each level of the health system.", "ProcedureForTroubleshooting": "What is the procedure for troubleshooting resource availability (stockout of registers/forms, device malfunction, etc.)?", "Discrepancies": "Describe any discrepancies in the planned and actual allocation and troubleshooting of equipment availability for surveillance, at all levels of the health system."}, "Indicator_2_5_1": {"ResponseDesc": "Indicate the funding for malaria surveillance. Please use the same currency throughout in order to calculate the gap between the budget and the funding available for malaria surveillance.", "EstimatedNationalBudget": "Total estimated national budget for malaria surveillance (From any source)", "FundsAvailableForMalariaSurveillance": "Funds available for malaria surveillance (including HR, equipment, maintenance, etc.) which is funded (by domestic and/or donor funding) ", "DomesticFunds": "Domestic funds available for malaria surveillance", "Currency": "<PERSON><PERSON><PERSON><PERSON>", "Value": "Value", "PercentageGap": "% gap", "SubnationalAreas": "What proportion of subnational areas (regions, districts, as applicable) receive funding specifically for IPTp surveillance ?", "SubnationalAreasPlaceholder": "$", "FundingAreas": "Is funding sufficient and sustainable at all levels of the health system ? <br/> What are the gaps ? What are the challenges around advocating for support (funding and partner) for surveillance ?", "FundingAreasPlaceholder": "e.g. competing priorities within the malaria program, reductions in overall national budget for health and/or malaria, <br/> challenges with financial management/absorption, etc.", "ITPITextboxLableOne": "What proportion of subnational areas (regions, districts, as applicable) receive funding specifically for IPTi surveillance ?", "ITPITextboxLableTwo": "Is funding sufficient and sustainable at all levels of the health system? <br/> What are the gaps?What are the challenges around advocating for support (funding and partner) for surveillance?", "ITPITextboxPlaceholder": "e.g. competing priorities within the malaria program, reductions in overall national budget for health and/or malaria, challenges with financial management/absorption, etc.", "SMCTextboxLabelProportion": "What proportion of subnational areas (regions, districts, as applicable) receive funding specifically for SMC surveillance ? ", "ITNRoutinetextboxLabel": "What proportion of subnational areas (regions, districts, as applicable) receive funding specifically for ITNs surveillance ? ", "MDATextboxLabelProportion": "What proportion of subnational areas (regions, districts, as applicable) receive funding specifically for MDA surveillance ? ", "PercentageDifference": "% difference", "PercentageDifferenceNote": "A positive value indicates a % gap in funding available whilst a negative value indicates a surplus of funds available"}}, "DRObjective_3_Responses": {"Indicator_3_1_2": {"RDTResponseDesc": "Calculate the proportion of service-delivery points that experienced stock-outs of rapid diagnostic tests (RDTs) in the previous 6 months. This indicator can only be assessed if an LMIS or commodity tracking system is available at the national level. If this data is not available then it should be recorded that this indicator cannot be assessed.", "RDTs": "RDTs", "Microscopy": "Microscopy", "MicroscopyResponseDesc": "Calculate the proportion of service-delivery points that experienced stock-outs of equipment required for microscopy in the previous 6 months (e.g slides, microscopes, reagents).", "MetNotMetTooltip": "Burden reduction: The proportion of health facilities with stock outs for RDTs is considered; <br/>  Met  <= 20% <br/> Partially met  21-50% <br/> Not met >50% <br/> Elimination:  The proportion of health facilities with stock outs for RDTs AND microscopy is considered; <br/> Met if both are <= 20%  <br/> Partially met if both are between  21-50% OR one of RDTs or microscopy is  <= 20%  <br/> Not met if both RDTs and microscopy > 50%", "ResponseError": "Please complete RDTs AND Microscopy details to Finalize the Indicator "}, "Indicator_3_1_3": {"ResponseDesc": "Calculate the proportion of service-delivery points that experienced stock-outs of ACTs and/or other front-line treatments in the previous 6 months. This indicator can only be assessed if an LMIS or commodity tracking system is available at the national level. If this data is not available then it should be recorded that this indicator cannot be assessed.", "MetNotMetTooltip": "The proportion of health facilities with stock outs for ACTs is considered; <br/> Met  <= 20% <br/> Partially met  21-50% <br/> Not met >50%"}, "Indicator_3_2_1": {"ResponseDesc": "What tools are used to record data for surveillance?", "RecordingTool": "Recording Tool", "RecordingTool1": "Recording tool 1", "RecordingTool2": "Recording tool 2", "RecordingTool3": "Recording tool 3", "NameOfRecordingTool": "Name of recording tool/ source document", "ToolType": "Tool type", "ToolTypePlaceholder": "e.g. electronically on an internet-based system or on a local system, paper registers, etc.) and the device and version if applicable", "YearToolWasIntroduced": "Year tool was introduced", "YearToolPlaceholder": "e.g. 2015", "FrequenceyDataIsRecorded": "Frequency data is recorded", "ResponsiblePerson": "Person responsible:", "ListOfVariablesRecorded": "List of variables recorded", "Screenshot": "Screenshot", "DateOfLastUpdated": "Date of last update", "TotalNumberOfRecordingTools": "Total number of recording tools", "OpdRegister": "e.g. OPD register", "InternetBasedSystem": "e.g. electronically on an internet-based system or on a local system, paper registers, etc.) and the device and version if applicable", "EgYear": "e.g. 2015", "DailyMonthly": "e.g. daily, monthly, on-going (case-by-case), before and after implementation, during the transmission season", "HealthCareWorker": "[role] e.g. health care workers, data entry clerk, technician, volunteers", "NameOfVariables": "[names of variables] e.g. name, age, sex, diagnosis", "Eg2013": "e.g 2013", "ITPIResponse": "What tools are used to record data for IPTi surveillance?", "GenomicResponse": "For each step in the data flow process, consider all surveillance activities for genomic surveillance. Tools and procedures for all should be included", "IPTIReportingToolSourceDocument": "Name of recording tool/source document", "IPTITotalNumberOfRecordingTools": "Total number of recording tools", "DrugEfficacyResponseDesc": "For each step in the data flow process, consider all surveillance activities for drug efficacy. Tools and procedures for all should be included", "CommodityResponseDesc": "For each step in the data flow process, consider all surveillance activities for commodity tracking data. Tools and procedures for all should be included", "NameOfRecordingToolSourceDocument": "Name of recording tool/ source document", "AssessChildInformationDialogTitle": "Assess Indicator 3.2.3", "MetNotMetTooltip": "This indicator is considered; <br/> Met if there is 1 tool <br/> Partially met if there are 2 tools <br/> Not met if there are 3+ tools", "AssessChildInformationDialogContent": "Changes in indicator 3.2.1 will affect the data of indicator 3.2.3. Please assess indicator 3.2.3.", "IRSReportingToolSourceDocument": "Name of recording tool/source document", "IRSResponse": "What tools are used to record data for IRS surveillance?", "IRSTitle": "The checklists should be completed for all variables listed to complete the assessment of this indicator.", "IPTPReportingToolSourceDocument": "Name of recording tool/source document", "IPTPTotalNumberOfRecordingTools": "Total number of recording tools", "IPTPResponse": "What tools are used to record data for IPTp surveillance?", "LarvalResponse": "What tools are used to record data for larval source management surveillance?", "EntoResponse": "What tools are used to record data for entomology surveillance?", "ITNMassResponse": "What tools are used to record data for ITN surveillance?", "ITNResponse": "What tools are used to record data for ITN surveillance?", "MdaResponseDesc": "What tools are used to record data for MDA surveillance?", "SMCResponseDesc": "What tools are used to record data for SMC surveillance?", "ResponseDescMalaria": "What tools are used to record data for malaria surveillance?", "InformationSystem": "Please add this information to the main data flow diagram that you have developed for objective 3.", "MandatoryFieldError": "Please fill in additional information for additional tools or delete if blank"}, "Indicator_3_2_2": {"ResponseDesc": "Using the list of variables recorded from each tool, calculate the proportion of malaria variables recorded:", "NoteToCompleteAssessment": "The checklists in sections A and B should be completed for all variables listed to complete the assessment of this indicator", "SerialNo": "No.", "VariableNumber": "Variable No.", "RecordedSourceDocuments": "Recorded in source documents", "Disagregation": "Disaggregation", "Under5": "Under 5", "Over5": "Over 5", "Sex": "Sex", "PregnantWoman": "Pregnant woman", "HealthSector": "Health sector (Public, private community)", "Geography": "Geography", "Other": "Other", "AllCauseOutPaients": "All-cause outpatients (including malaria)", "SuspectedMalariaCaseCauseOutPatients": "Suspected malaria cases", "PresumedMalariaCases": "Presumed malaria cases", "RDTTested": "RDT Tested", "MicroscopyTested": "Microscopy tested", "ConfirmedMalariaCase": "Confirmed malaria cases", "ConfirmedMalariaCasesBySpecies": "Confirmed malaria cases by species", "RDTpositive": "RDT positive", "MicroscopyPositive": "Microscopy positive", "AllCauseInpatients": "All cause inpatients (including malaria)", "MalariaInpatients": "Malaria inpatients*", "AllCauseInpatientDeaths": "All-cause inpatient deaths", "WHOVariablesRecorded": "% of WHO recommended variables recorded", "Assessment": "Assessment", "Checklist": "Checklist", "CorrectlyDistinguisedPresumedCases": "Presumed cases are correctly distinguished from confirmed cases", "RDTConfirmCases": "RDT confirmed cases are distinguished from microscopy cases", "FaclciparumInfections": "P. falciparum infections are distinguished from <PERSON><PERSON> vivax, <PERSON><PERSON> malariae, <PERSON><PERSON> ovale, <PERSON><PERSON>i and mixed", "IndigenousCases": "Indigenous cases are distinguished from all other classifications (introduced, imported, induced, recurrent) ", "IndifiedCasesThroughProactiveCaseDetection": "Cases identified through proactive case detection are distinguished from those detected through reactive case detection", "ResponseCheckList": "The checklists should be completed for all variables listed to complete the assessment of this indicator", "IPTPFirstDoseGiven": "Number of 1st IPT dose SP given", "IPTPSecondDoseGiven": "Number of 2nd IPT dose SP given", "IPTPThirdDoseGiven": "Number of 3rd IPT dose SP given", "IPTPFourthDoseGiven": "Number of 4th IPT dose SP given", "IPTPClinicVisit": "Number of first antenatal clinic visits", "ITPIResponse": "Using the list of variables recorded from each tool, calculate the proportion of IPTi variables recorded:", "SMCResponseDesc": "Using the list of variables recorded from each tool, calculate the proportion of SMC variables recorded:", "SMCNoteToCompleteAssessment": "The checklists should be completed for all variables listed to complete the assessment of this indicator", "FullNoOfCourses": "No. of children aged 3–59 months who received the full no. of courses of IPTi", "OneDoseOfITPI": "No. of children who received one dose of IPTi", "TwoDoseOfITPI": "No. of children who received two doses of IPTi", "ThreeDoseOfITPI": "No. of children who received three doses of IPTi", "RequiringITPI": "No. of children aged 3–59 months requiring IPTi", "ITNRoutineResponseDesc": "Using the list of variables recorded from each tool, calculate the proportion of ITN variables recorded: ", "ITNRoutineTitle": "The checklists should be completed for all variables listed to complete the assessment of this indicator", "ITNDistributed": "ITN distributed through routine channels (ANC, EPI/well baby clinic)", "ThroughANC": "Number of ITN distributed through ANC", "ThroughEPI": "Number of ITN distributed through EPI/well baby clinics", "ThroughOther": "Number of ITN distributed through other channels", "PopulationAtRisk": "Population at risk", "GenomicResponseDesc": "Using the list of variables recorded from each tool, calculate the proportion of genomic surveillance variables recorded:", "GenomicResponseTitle": "The checklists should be completed for all variables listed to complete the assessment of this indicator", "GenomicNumberSample": "Number of samples screened for resistance biomarkers", "GenomicResistance": "Resistance biomarkers screened for", "GenomicResitanceMutation": "Number of samples with resistance mutation (by resistance marker)", "GenomicWT": "Number of samples with WT", "GenomicBiomarker": "Number of samples screened for biomarker hrp 2/3", "GenomicHRP2": "Number of samples with HRP2 deletions", "GenomicHRP3": "Number of samples with HRP3 deletions", "GenomicHRP2And3": "Number of samples with HRP2 AND HRP3 deletions", "GenomicPFHRP": "Number of pfhrp2/3 deletions causing negative RDT results", "GenomicTotalPF": "Total number of Pf cases diagnosed by either by RDT or microscopy", "IRSResponseDesc": "Using the list of variables recorded from each tool, calculate the proportion of IRS variables recorded: ", "EndoResponseDesc": "Using the list of variables recorded from each tool, calculate the proportion of entomology variables recorded:", "EndoTitle": "The checklists in this section should be completed for all variables listed to complete the assessment of this indicator", "NoOfEntomological": "Number of entomological sentinel sites ", "NoOfAdult": "Total number of  adult anopheles mosquitoes collected", "NoOfCollections": "Number of collections made in a given time period", "TotalAdultAnopheles": "Total number of  adult anopheles mosquitoes collected of a specific species", "AnophelesFemale": "Number of adult anopheles female mosquitoes that attempt to feed or are freshly blood-fed, per person per unit time.", "BloodFedAdult": "Number of blood-fed adult anopheles female mosquitoes that feed on human blood", "AnophelesVector": "Number of anopheles vector from which the blood meal was identified", "SuccessfulBloodFeed": "Number of adult anopheles female mosquitoes which attempted to bite or successful blood-feed indoors.", "BloodFeedOutdoors": "Number of adult anopheles female mosquitoes which attempted to bite or successful blood-feed outdoors", "RestingIndoor": "Number of adult anopheles female mosquitoes collected resting indoors in structures sampled.", "RestingOutdoor": "Number of adult anopheles female mosquitoes collected resting outdoors in structures sampled, usually per human–hour", "FemaleMosquitoDead": "Number of adult female mosquitoes dead after exposure to  a discriminating concentration of an insecticide", "FemaleVectorExposed": "Total number of adult anopheles female vector mosquitoes exposed to a discriminating concentration of an insecticide", "InsecticideStatus": "Insecticide resistance mechanism status", "InfectedBites": "Number of infective bites received per person in a given unit of time, in a human population", "PotentialHabitats": "Number of potential habitats for Anopheles vector egg-laying and immature stage development identified", "AquaticHabitats": "Number of aquatic habitats found to harbour Anopheles vector larvae or pupae out of the total potencial habitats for Anopheles vector egg-laying and inmature stage development", "Sporozoites": "Number of adult female anopheline mosquitoes identified as having sporozoites in their salivary glands ", "CommodityResponseDesc": "Using the list of variables recorded from each tool, calculate the proportion of commodity tracking data variables recorded", "CommodityPreRDTStock": "Number of prequalified RDT stock on-hand", "CommodityACTStock": "Number of ACT stock on-hand", "CommodityLLINStock": "Number of LLINs stock on hand", "CommodityInsectisideStock": "Number of insecticide stock on hand", "CommodityRDTStock": "Number of RDTs dispensed", "CommodityRDTUse": "Number of RDTs used", "CommodityRDTTested": "Number of RDTs tested", "CommodityACTDispense": "Number of ACTS dispensed", "CommodityACTUse": "Number of ACTs used", "CommodityHealthFacilityWithStock": "Number of health facility with no stock-outs of key commodities for diagnostic testing", "CommodityHealthFacilityWithoutStock": "Number of health facility without stock- outs of first-line treatment", "CommodityStockOutReportInsecticides": "Number of stockouts reported for insecticides (during the period under review)", "CommodityStockOutReportLLIN": "Number of stockouts reported for LLINS (during the period under review)", "CommodityNumACTDiscarded": "Number of ACTs discarded", "CommodityNumRDTDiscarded": "Number of RDTs discarded", "CommodityDelivery": "Delivery (shipment) time (number of days between order and delivery)", "LarvalResponseDesc": "Using the list of variables recorded from each tool, calculate the proportion of larval source management variables recorded:", "ResponseError": "Please select 'Yes' or 'No' for 'Recorded in source documents' for all variables to finalize the indicator", "ResponseErrorStepB": "Please select 'Yes' or 'No' for all assessment checklist and provide details after selecting 'No' to finalize the indicator", "MetNotMetTooltip": "If % of WHO recommended variables recorded is <br/> >= 80% = Met <br/> 50% - 79% = Partially met <br/> < 50% = Not met", "IPTpResponseDesc": "Using the list of variables recorded from each tool, calculate the proportion of IPTp variables recorded:", "RecordedInSourceDocuments": "Recorded in source documents", "IRSNoteToCompleteAssessment": "The checklists should be completed for all variables listed to complete the assessment of this indicator", "IRSRecording": "Using the list of variables recorded from each tool, calculate the proportion of IRS variables recorded:", "DrugEfficacyResponseDesc": "Using the list of variables recorded from each tool, calculate the proportion of drug efficacy variables recorded:", "DrugEfficacyResponseTitle": "The checklists should be completed for all variables listed to complete the assessment of this indicator", "ResponseErrorAToB": "Please complete Step A to B to finalize the indicator", "HasPVivaxCasesQuestion": "Do you have any <i>P.vivax</i> cases?", "HasMalariaInpatientsQuestion": "Do you routinely hospitalise all malaria cases?", "ToolTipMalariaInpatients": "In elimination settings, some countries require all patients infected with malaria to be hospitalised for at least the first three days of their treatment to ensure adherence. If this is the policy then this variable would not be collected.", "WHOBRVariablesRecorded": "% of WHO recommended variables recorded for burden reduction", "WHOEliminationVariablesRecorded": "% of WHO recommended variables recorded for elimination"}, "Indicator_3_2_3": {"RecordingTool": "Recording tool", "RecordingTool1": "Recording tool 1", "RecordingTool2": "Recording tool 2", "RecordingTool3": "Recording tool 3", "RecordingTool4": "Recording tool 4", "RecordingName": "Name", "RecodringFormsAndTools": "Recording forms/tools are standardized across all service-delivery points", "DetailsOnPrivatePublicUseStandardisedForm": "Details on whether private and public use standardised forms, the year where new forms came into use and whether there are parts of the country still using old forms ", "SaveWarning": "Please save this indicator so that the data will match with indicator 3.2.1", "ResponseError": "Please select 'Yes' or 'No' and provide details for the recording tool to finalize the indicator", "MetNotMetTooltip": "Recording forms/tools are standardized across all service-delivery points is considered; <br/> Met if the response is yes (√) <br/>  Not met if the response is no (x)", "NoRecordingToolFound": "NO RECORDING TOOL FOUND", "IndicatorParentDependentMessage": "Please Assess 3.2.1 and create at least one recording tool Or select 'This indicator cannot be assessed' to finalize the indicator", "ResponseErrorElimination": "Please add for at least one recording tool and select 'Yes' or 'No' and provide details for the recording tool to finalize the indicator"}, "Indicator_3_3_1": {"ToolelectedSame3.2.1": "The tool selected is the same as in table 3.2.1", "ResponseDesc": "Are reporting tools same as the recording tools in Indicator 3.2.1 ?", "NotNeedToFill": "User does not need to fill out the details again as reporting tools for this indicator are same as recording tools in indicator 3.2.1.", "NoteInfo": "If yes is selected, this table does not need to be filled again.", "ToolsUsedToReportData": "What tools are used to report data from the point of data recording to subnational and national levels?", "NameOfReportingToolSourceDocument": "Name of reporting tool/ source document", "NameOfReportingToolSourceDocumentPlaceholder": "e.g.Monthly case surveillance report", "ToolType": "Tool type", "ToolTypePlaceholder": "e.g. electronically on an internet-based system or on a local system, paper registers, etc.) and the device and version if applicable", "YearToolWasIntroduced": "Year tool was introduced", "YearToolPlaceholder": "e.g. 2015", "HealthSystemLevelReporting": "Health system level of reporting", "HealthSystemLevelPlaceholder": "e.g health facility to district", "MethodOfReportingTransmission": "Method of reporting/ transmission", "MethodOfReportingTransmissionPlaceholder": "[how data are sent to the next level] e.g. mail, email, SMS, online database, etc.", "Aggregation": "Aggregation:", "AggregationPlaceholder": "[level of data reported] e.g. from case-based data to health facility counts, etc.", "FrequencyDataReported": "Frequency data is reported:", "CaseByCasePlaceholder": "e.g. daily, monthly, on-going (case-by-case), before and after implementation, during the transmission season, etc. ", "PersonResponsibleForReporting": "Person responsible for reporting:", "PersonResponsiblePlaceholder": "[role] e.g. health care workers, technician, volunteers", "RecipientOfReports1": "Recipient of reports", "RecipientOfReports1Placeholder": "[ role] e.g. health care workers, technician, volunteers", "RecipientOfReports2Placeholder": "[role] e.g. supervisor, stock warehouse, NMP, ", "RecipientListVariablesReported": "List of variables reported", "RecipientListVariablesReportedPlaceholder": "[names of variables] e.g. name, age, sex, dignosis", "LinkOrScreenshot": "Link or Screenshot", "TotalNumberOfReportingTools": "Total number of reporting tools", "ReportingTool": "Reporting tool", "AssessChildInformationDialogTitle": "Assess Indicator 3.3.1", "AssessChildInformationDialogContent": "Changes in indicator 3.3.1 will affect the data of indicator 3.3.3. Please assess indicator 3.3.3", "NoRecordingToolFound": "NO RECORDING TOOL FOUND", "IndicatorParentDependentMessage": "Please Assess 3.2.1 and create at least one recording tool Or select 'This indicator cannot be assessed' to finalize the indicator"}, "Indicator_3_3_2": {"ResponseDesc": "Using the list of variables reported from each tool, calculate the proportion of malaria variables reported:", "ResponseTitle": "The checklists should be completed for all variables listed to complete the assessment of this indicator.", "TableFooterTitle": "% of WHO recommended variables recorded ", "No.": "No.", "Variables": "Variable", "Variables_No": "Variable No.", "Variables_No_Variable": "Variable No.   Variable", "RecordedInSourceDocuments": "Reported to subnational and national levels", "IndicatorMonitoredInRoutineOutputs": "Indicator monitored in routine outputs", "Disagregation": "Disaggregation", "Under5": "Under 5", "Over5": "Over 5", "Sex": "Sex", "PregnantWoman": "Pregnant woman", "HealthSector": "Health sector (Public, private community)", "Geography": "Geography", "Other": "Other", "AllCauseOutpatients": " All - cause outpatients(including malaria)", "SuspectedMalariaCases": "Suspected malaria cases", "PresumedMalariaCases": "Presumed malaria cases", "RDTTested": "RDT tested", "MicroscopyTested": "Microscopy tested", "ConfirmedMalariaCases": "Confirmed malaria cases", "ConfirmedMalariaCasesSpecies": "Confirmed malaria cases by species", "RDTPositive": "RDT positive ", "MicroscopyPositive": "Microscopy positive", "AllCauseInpatients": "All cause inpatients (including malaria)", "MalariaInpatients": "Malaria inpatients*", "AllCauseInpatientDeaths": "All-cause inpatient deaths ", "Indicators": "Indicators", "SMCResponseDesc": "Using the list of variables recorded from each tool, calculate the proportion of SMC variables recorded:", "IPTIResponse": "Using the list of variables recorded from each tool, calculate the proportion of IPTi variables recorded: ", "IPTITitle": "The checklists should be completed for all variables listed to complete the assessment of this indicator.", "ITNRoutineResponse": "Using the list of variables reported from each tool, calculate the proportion of ITN variables reported", "ITNRoutineTitle": "The checklists should be completed for all variables listed to complete the assessment of this indicator", "ITNMassNoOfITN": "Number of ITN distributed through mass campaigns", "IRSResponseDesc": "Using the list of variables recorded from each tool, calculate the proportion of IRS variables recorded: ", "LarvalResponseDesc": "Using the list of variables recorded from each tool, calculate the proportion of larval source management variables reported: ", "CommodityResponseDesc": "Using the list of variables reported from each tool, calculate the proportion of commodity tracking data variables reported:", "EntoResponseDesc": "Using the list of variables reported from each tool, calculate the proportion of entomology variables reported:", "ResponseError": "Please select 'Yes' or 'No' for 'Reported to subnational and national levels' all variables to finalize the indicator", "MetNotMetTooltip": "If % of WHO recommended variables recorded is <br/> >= 80% = Met <br/> 50% - 79% = Partially met <br/> < 50% = Not met", "GenomicResponseDesc": "Using the list of variables reported from each tool, calculate the proportion of genomic surveillance variables reported:", "IRSTitle": "The checklists should be completed for all variables listed to complete the assessment of this indicator.", "DrugEfficacyResponseDesc": "Using the list of variables reported from each tool, calculate the proportion of drug efficacy variables reported:", "LarvalTitle": "The checklists should be completed for all variables listed to complete the assessment of this indicator.", "EntoRoutineTitle": "The checklists should be completed for all variables listed to complete the assessment of this indicator", "IPTpResponseDesc": "Using the list of variables reported from each tool, calculate the proportion of IPTp variables reported:", "HasPVivaxCasesQuestion": "Do you have any <i>P.vivax</i> cases?", "HasMalariaInpatientsQuestion": "Do you routinely hospitalise all malaria cases?", "ToolTipMalariaInpatients": "In elimination settings, some countries require all patients infected with malaria to be hospitalised for at least the first three days of their treatment to ensure adherence. If this is the policy then this variable would not be collected."}, "Indicator_3_3_3": {"ReportingTool": "Reporting tool", "ReportingName": "Name", "IsStandardized": "Reporting tools are standardized across all service-delivery points", "DetailsOnPrivatePublicUseStandardisedForm": "Details on whether private and public use standardised forms, the year where new forms came into use and whether there are parts of the country still using old forms ", "ReportingTool1": "Reporting tool 1", "ReportingTool2": "Reporting tool 2", "ReportingTool3": "Reporting tool 3", "ReportingTool4": "Reporting tool 4", "SaveWarning": "Please save this indicator so that the data will match with indicator 3.3.1", "NoRecordingToolFound": "No Reporting Tool Found", "IndicatorParentDependentMessage": "Please assess indicator 3.3.1 before assessing this indicator"}, "Indicator_3_3_4": {"ResponseDesc": "Use the checklist below to identify if the criteria for the malaria case surveillance process adheres to WHO guidance. Further details are given in \"How to assess\"", "HighModerate": "High/ moderate ≥ 10% PfPR or >250 per 1000 API", "Low": "Low 1–10% PfPR or 100–250 per 1000 API", "VeryLow": "Very low/ zero/ maintaining zero < 1% PfPR or < 100 per 1000 API", "CriteriaMet": "Criteria Met", "ReportingFrequency": "Reporting frequency", "AggregationOfReportedData": "Aggregation of reported data", "ReportingOfZeroCases": "Reporting of zero cases", "ProportionOfCrietriaMet": "Proportion of crietria met", "Monthly": "Monthly", "Weekly": "Weekly", "ImmediateCaseNotification": "Immediate case notification", "AggregateCasesBySexAndAgeCategory": "Aggregate cases by sex and age category", "CaseClassification": "Case report, age, sex, residence, travel history  clinical features and occupation and case classification", "ReportingWhenNoMalariaCases": "Reporting occurs even when no malaria cases occur ", "ResponseError": "Please select 'Yes' or 'No' for all criteria to finalize the indicator"}, "Indicator_3_4_2": {"ResponseDesc": "Using the list of indicators included in each output, calculate the proportion of malaria- indicators included.", "ResponseTitle": "The checklists should be completed for all variables listed to complete the assessment of this indicator.", "TableFooter": "% of WHO recommended indicators analyzed", "TableFooterOne": "*List biomarkers (e.g Pfcrt SNP, Pfmdr1 (86Y), Pfpm2-3, Pfdhfr SNP, Pfdhps SNP, Pfmdr1, Pfmdr1 (N86), PfK13 SNP, Pfcytb SNP)", "No.": "Variable No.", "Indicators": "Indicators", "Disagregation": "Disaggregation", "Under5": "Under 5", "Over5": "Over 5", "Sex": "Sex", "PregnantWoman": "Pregnant woman", "HealthSector": "Health sector (Public, private community)", "Geography": "Geography", "Other": "Other", "MethodOfConfirmation": "Method of confirmation", "ProportionOfSuspectsTested": "Proportion of suspects tested", "PatientsTestedForMalaria": "Number of patients tested for malaria ", "ConfirmedMalariaCases": "Number of confirmed malaria cases", "PresumedMalariaCases": "Number of presumed malaria cases", "TotalMalariaCases": "Total malaria cases (confirmed + presumed) ", "TestPositivityRate": "Test positivity rate", "ProportionFalciparumCases": "Proportion of P.falciparum cases", "ProportionVivaxCases": "Proportion of P.vivax cases", "CrudeCaseIncidence": "Crude case incidence", "ProportionMalariaOutpatients": "Proportion of malaria outpatients", "NumberMalariaInpatients": "Number of malaria inpatients", "ProportionMalariaInpatients": "Proportion of malaria inpatients", "ResponseProportionDesc": "Using the list of indicators included in each output, calculate the proportion of relevant indicators included.", "IPTIResponseDesc": "Using the list of indicators included in each output, calculate the proportion of IPTi indicators included.", "IPTIproportionOfSuspectsTested": "Number of 1st IPT dose SP given", "IPTIpatientsTestedForMalaria": "Number of 2nd IPT dose SP given", "IPTIconfirmedMalariaCases": "Number of 3rd IPT dose SP given", "IPTIpresumedMalariaCases": "Number of 4th IPT dose SP given", "IPTItotalMalariaCases": "Number of first antenatal clinic visits", "MDATableFooterPercentTitle": "% of WHO recommended indicators analyzed", "iptpTableFooter": "% of WHO recommended variables recorded", "IRSResponseDesc": "Using the list of indicators included in each output, calculate the proportion of IRS indicators included.", "ITPITableFooter": "% of Indicators analyzed", "MDAResponseDesc": "Using the list of indicators included in each output, calculate the proportion of MDA indicators included.", "IRSNoteTitle": "The checklists should be completed for all indicators listed to complete the assessment of this indicator", "CommodityTableFooter": "% of WHO recommended indicators analyzed", "ResponseError": "Please select 'Yes' or 'No' for 'Indicator monitored in routine outputs' for all indicators to finalize the indicator", "MetNotMetTooltip": "If % of WHO recommended indicators recorded is <br/> >= 80% = Met <br/> 50% - 79% = Partially met <br/> < 50% = Not met", "IndicatorMonitoredInRoutineOutputs": "Indicator monitored in routine outputs", "GenomicResponseDesc": "Using the list of indicators included in each output, calculate the proportion of genomics indicators included.", "IRSIndicatorMonitor": "Recorded in source documents", "IptpResponseDesc": "Using the list of indicators included in each output, calculate the proportion of IPTp indicators included.", "ITNMassResponseDesc": "Using the list of indicators included in each output, calculate the proportion of ITN indicators included.", "ITNMassNoteTitle": "The checklists should be completed for all variables listed to complete the assessment of this indicator", "ITNResponseDesc": "Using the list of indicators included in each output, calculate the proportion of ITN indicators included.", "ITNNoteTitle": "The checklists should be completed for all variable listed to complete the assessment of this indicator", "CommodityResponseProportionDesc": "Using the list of indicators included in each output, calculate the proportion of commodity tracking indicators included.", "DrugEfficacyResponseProportionDesc": "Using the list of indicators included in each output, calculate the proportion of drug efficacy indicators included.", "EntomologyResponseDesc": "Using the list of indicators included in each output, calculate the proportion of entomology indicators included.", "SMCResponseDesc": "Using the list of indicators included in each output, calculate the proportion of SMC indicators included.", "TableFooterTitle": "% of Indicators analyzed "}, "Indicator_3_4_1": {"ResponseDesc": "What are the expected outputs from routine analysis of data from malaria surveillance, such as dashboards, bulletins, and other regular reports? List each in the columns of the table", "NoteToCompleteAssessment": "The information should be filled for at least one output to complete the assessment of this indicator. If there are no outputs please enter none in the text boxes", "Output1": "Output 1", "Output2": "Output 2", "Output3": "Output 3", "Output4": "Output 4", "Output5": "Output 5", "Output6": "Output 6", "NameTypeExpectedOutput": "Name/type of the expected output", "DataSourceUsed": "Data source(s) used", "IndicatorsAndVisualizations": "List of indicators and visualizations included", "ToolsUsed": "Tools used", "Method": "Method", "FrequencyOfAnalysis": "Frequency of analysis or output production", "LevelOfAnalysisDone": "Level at which the analysis is done", "PersonResponsible": "Person responsible", "Recipients": "Recipient(s)", "MethodOfDissemination": "Method of dissemination:", "Screenshot": "Screenshot", "TotalExpectedOutputs": "Total number of expected outputs", "NameTypeExpectedOutputPlaceholder": "e.g. monthly bulletin", "DataSourceUsedPlaceholder": "[names of data used] e.g. LIMS data and census. If multiple datasets are used, identify if/how are these linked/merged", "IndicatorsAndVisualizationsPlaceholder": "[names of indicator] e.g. confirmed malaria cases, positive cases treated with an ACT etc.", "ToolsUsedPlaceholder": "[names of tools used] e.g. Excel", "MethodPlaceholder": "[description of methods used] e.g. analysis is automated, analysis requires export from system to externally link with other datasets, perform cross-tabulation, mapping, etc.)", "FrequencyOfAnalysisPlaceholder": "e.g. monthly", "LevelOfAnalysisDonePlaceholder": "e.g. by the districts ", "PersonResponsiblePlaceholder": "[cadre/ role] e.g. records officer", "RecipientsPlaceholder": "[cadre/ role] e.g. program manager, health faciality staff, etc. ", "OutputSharedPlaceholder": "[how output is shared] e.g. email, posters, meetings"}, "Indicator_3_5_1": {"ResponseDesc": "What mechanisms and processes are in place for data quality assurance and validation of data for malaria surveillance? <br/> (For each): Describe the activities used to ensure quality of malaria surveillance data", "ResponseTitle": "Information must be completed for each activity to complete the assessment for this indicator", "DataCleaning": "Data cleaning", "DataReviewMeetings": "Data review meetings", "DataQualityAssessments": "Data quality assessments(routine or part of supervision)", "MonitoringDataQuality": "Monitoring of data quality indicators", "DataQualityAssurance": "Data quality assurance procedure in place", "FrequencyValidation": "Frequency of validation activity", "DataValidated": "Data validated", "ToolsAndMethods": "Tools and methods used", "LevelOfValidation": "Level of at which validation is done", "PersonResponsible": "Person responsible", "FollowUpAction": "Follow up action", "LinkCopyReports": "Link to copy of reports or other output material", "ToolTipTitle": "Proportion of expected quality assurance procedures in place", "ToolTipDetails": "e.g =X/4*100 (X = Number of 'Yes' from 'Data Quality Assurance Procedure in Place' row, how many are filled)", "egMonthly": "e.g. monthly", "MultipleDatasets": "[names of data source] e.g. HMIS. If multiple datasets are used, identify if/how are these linked/merged", "egRegisters": "e.g registers, HMIS", "NameOfTools": "[names of tools used] e.g. Excel", "DataQualiyMeasures": "e.g. data quality measures for each facility within district calculated in Excel and reviewed at a forum ", "AdminLevel": "[admin level] e.g. district ", "DistrictLead": "[role] e.g. district lead", "AuditTrails": "e.g. audit trails, etc.", "MeetingReport": "e.g. meeting report and action plan", "AssessmentResults": "e.g. assessment results", "GraphicsTables": "e.g list of indicators and graphics/tables", "SMCResponseDesc": "What mechanisms and processes are in place for data quality assurance and validation of data for SMC?", "MDAResponseDesc": "What mechanisms and processes are in place for data quality assurance and validation of data for MDA?", "IRSResponseDesc": "What mechanisms and processes are in place for data quality assurance and validation of IRS data <br/> {For each}: Describe the activities used to ensure quality of IRS data", "LarvalResponseDesc": "What mechanisms and processes are in place for data quality assurance and validation of IRS data <br/> {For each}: Describe the activities used to ensure quality of Larval source management data", "ResponseError": "Please select 'Yes' or 'No' for all 'Data quality assurance procedure in place' row to finalize the indicator", "MetNotMetTooltip": "100% = Met <br/> 1%-99% = Partially met <br/> 0% = Not met"}, "Indicator_3_5_2": {"ResponseDesc": "Enter the no. of times in the previous 12 months that malaria surveillance data has been assessed for quality at the national level", "ResponseTitle": "e.g 4 if review frequency is quarterly and data were reviewed every quarter.", "TextBoxCommentPlaceholder": "Comment on whether this is satisfactory", "TextboxLabel": "Number of times in the previous 12 months that ITNs surveillance data has been assessed for quality at the national level", "TextBoxPlaceholder": "e.g 4 if review frequency is quarterly and data were reviewed every quarter. Comment on whether this is satisfactory", "SMCTextboxLabel": "Number of times in the previous 12 months that SMC surveillance data has been assessed for quality at the naitonal level", "MDATextboxLabel": "No. of times in the previous 12 months that MDA data has been assessed for quality at the national level", "IRSTextboxLabel": "No. of times in the previous 12 months that IRS data has been assessed for quality at the national level", "LarvalTextboxLabel": "Number of times in the previous 12 months that Larval source management surveillance data has been assessed for quality at the naitonal level", "DrugEfficacyTextboxLabel": "Number of times in the previous 12 months that Drug Efficacy surveillance data has been assessed for quality at the national level", "CommodityTextboxLabel": "Number of times in the previous 12 months that commodity tracking surveillance data has been assessed for quality at the naitonal level", "EntoTextboxlabel": "No. of times in the previous 12 months that Entomology data has been assessed for quality at the national level", "MetNotMetTooltip": "This indicator is based on, and should be adjusted according to the frequency of data quality review, for example: if review frequency is quarterly and data were reviewed 4 times in 12 months = Met <br/> If data were  reviewed 1 - 3 times = Partially met <br/> If data were not reviewed at all = Not met", "IPTIResponseDesc": "Enter the no. of times in the previous 12 months that IPTi surveillance data has been assessed for quality at the national level", "MDAResponseDesc": "Enter the no. of times in the previous 12 months that MDA surveillance data has been assessed for quality at the national level", "DataCleaning": "Data cleaning", "DataReviewMeetings": "Data review meetings", "DataQualityAssessments": "Data quality assessments(routine or part of supervision)", "MonitoringDataQuality": "Monitoring of data quality indicators", "DataQualityAssurance": "Data quality assurance procedure in place", "FrequencyValidation": "Frequency of validation activity", "DataValidated": "Data validated", "egMonthly": "e.g. monthly", "ResponseError": "Please select 'Yes' or 'No' for all 'Data quality assurance procedure in place' row to finalize the indicator", "SMCResponseDesc": "Enter the no. of times in the previous 12 months that SMC surveillance data has been assessed for quality at the naitonal level", "IPTPResponseDesc": "Enter the no. of times in the previous 12 months that IPTp surveillance data has been assessed for quality at the national level", "GenomicResponseDesc": "Enter the no. of times in the previous 12 months that Genomic surveillance data has been assessed for quality at the national level", "IRSRoutineResponseDesc": "Enter the no. of times in the previous 12 months that IRS surveillance data has been assessed for quality at the naitonal level", "ITNMassResponseDesc": "Enter the no. of times in the previous 12 months that ITN surveillance data has been assessed for quality at the naitonal level", "DrugEfficacyResponseDesc": "Enter the no. of times in the previous 12 months that Drug efficacy surveillance data has been assessed for quality at the national level", "EntoResponseDesc": "Enter the no. of times in the previous 12 months that Entomology surveillance data has been assessed for quality at the national level", "CommodityResponseDesc": "Enter the no. of times in the previous 12 months that commodity tracking surveillance data has been assessed for quality at the national level", "LarvalResponseDesc": "Enter the no. of times in the previous 12 months that Larval source management surveillance data has been assessed for quality at the national level", "ITNRoutineResponseDesc": "Enter the no. of times in the previous 12 months that ITNs surveillance data has been assessed for quality at the naitonal level", "Quarterly": "Quarterly", "Monthly": "Monthly", "Weekly": "Weekly", "FrequencyOfValidationNumerator": "No. of times in the previous year that malaria surveillance data has been assessed for quality at the national level <br/> (Numerator) ", "FrequencyOfValidationDenominator": "Number of times malaria surveillance data is expected to be assessed annually <br/> (Denominator)", "FrequencyOfValidationActivity": "Frequency of assessment of data quality at the national level", "NationalFrequencyValidation": "Frequency of validation activity at the national level", "RegionalFrequencyValidation": "Frequency of validation activity at the regional level", "DistrictFrequencyValidation": "Frequency of validation activity at the district level", "MonitoringFrequency": "Monitoring frequency", "Annually": "Annually", "Biannually": "<PERSON><PERSON><PERSON><PERSON>"}, "Indicator_3_5_3": {"ResponseDesc": "Using the list of variables reported from each tool, calculate the proportion of malaria variables reported:", "DataQualityControlCheck": "Data Quality Control Check", "DataQualityControlCheckInPlace": "Data Quality Control Check in place", "BuiltInChecksAtDataEntry": "Automated or built in checks at data entry (e.g. data validation rules)", "ProducesDataVerificationReports": "Produces data verification reports or audit trail", "AvoidDuplicateEntryOfRecords": "Mechanism or process to avoid duplicate entry of records", "ProportionOfExpectedQualityControls": "Proportion of expected quality controls  included in electronic systems", "TableFooterProportionsTooltip": "e.g Burden reduction  =X/2*100 <br/> Elimination =X/3*100", "ResponseError": "Please select 'Yes' or 'No' for all 'Data quality control check in place' to finalize the indicator"}, "Indicator_3_6_1": {"ResponseDesc": "List who has access to what information at each health system level", "NoteToCompleteAssessment": "Additional information for at least the national level should be filled to complete the assessment of this indicator", "DataAccessDetails": "Data Access Details", "National": "National", "Subnational": "Subnational", "ServiceDelivery": "Service-delivery", "RolesOfIndividuals": "Roles of individuals with access to data", "MethodOfAccess": "Method of access: ", "WhatCanBeAccessed": "What can be accessed: ", "CanDataBeAccessed": "How often can data be accessed", "CanDataBeAccessedNational": "Can data be accessed by users at national health system level?", "CanDataBeAccessedSubnational": "Can data be accessed by users at subnational health system level?", "CanDataBeAccessedServiceDelivery": "Can data be accessed by users at service-delivery health system level?", "CanDataBeAccessedAtThisLevel": "Can data be accessed by users at this health system level?", "RolesOfIndividualsFirstPlaceholder": "(e.g. NMP, partners)", "RolesOfIndividualsSecondPlaceholder": "(e.g. district  surveillance officers)", "RolesOfIndividualsThirdPlaceholder": "(e.g. healthcare workers)", "MethodOfAccessFirstPlaceholder": "(e.g. direct access  from national database)", "MethodOfAccessSecondPlaceholder": "(e.g. direct access  from national database)", "MethodOfAccessThirdPlaceholder": "(e.g. direct access  from national database)", "WhatCanBeAccessedFirstPlaceholder": "(e.g. raw data, reports  and standardised dahsboards)", "WhatCanBeAccessedSecondPlaceholder": "( e.g standardised dahsboards and reports)", "WhatCanBeAccessedThirdPlaceholder": "(e.g. standardised dahsboards and patients line list )", "CanDataBeAccessedFirstPlaceholder": "(e.g. Daily)", "CanDataBeAccessedSecondPlaceholder": "(e.g. Monthly)", "CanDataBeAccessedThirdPlaceholder": "(e.g. Daily)", "ProportionOfHealthSystemLevels": "Proportion of health system levels that have access to data", "TableFooterProportionsTooltip": "e.g Number of health system levels with access/Number of health system levels*100", "ITPIResponseDesc": "List who has access to what information for IPTi at each health system level  (e.g. frontline staff have login in to DHIS2 or can request from manager)", "SMCResponseDesc": "List who has access to what information for SMC at each health system level  (e.g. frontline staff have login in to DHIS2 or can request from manager)", "MDAResponseDesc": "List who has access to what information for MDA at each health system level  (e.g. frontline staff have login in to DHIS2 or can request from manager)", "GenomicResponseDesc": "List who has access to what information for genomic surveillance at each health system level  (e.g. frontline staff have login in to DHIS2 or can request from manager)", "IRSResponseDesc": "List who has access to what information for IRS at each health system level", "ResponseError": "Please fill out all details for 'National Level' to finalize the indicator", "MetNotMetTooltip": "If proportion of health system levels that have access to data is <br/> 100% = Met <br/> 1-99% = Partially met <br/>  0% = Not met", "IPTPResponseDesc": "List who has access to what information for IPTp at each health system level", "DrugEfficacyResponseDesc": "List who has access to what information for drug efficacy at each health system level  (e.g. frontline staff have login in to DHIS2 or can request from manager)", "CommodityResponseDesc": "List who has access to commodity tracking data at each health system level", "LarvalResponseDesc": "List who has access to what information for larval source management at each health system level  (e.g. frontline staff have login in to DHIS2 or can request from manager)", "EntoResponseDesc": "List who has access to what information for entomology at each health system level  (e.g. frontline staff have login in to DHIS2 or can request from manager)", "ITNMassResponseDesc": "List who has access to what information for ITN at each health system level  (e.g. frontline staff have login in to DHIS2 or can request from manager)", "ITNResponseDesc": "List who has access to what information for ITN at each health system level  (e.g. frontline staff have login in to DHIS2 or can request from manager)"}}, "DRObjective_4_Responses": {"Indicator_4_1_1": {"ResponseDesc": "Identify and describe the health management structures that regularly review malaria surveillance data.", "Structures": "Structures", "InPlace": "In place", "SubnationalWorkPlan": "Subnational work plan that includes implementation activities", "SubnationalImplementationActivities": "A list of monitoring indicators for review of subnational implementation activities", "MonitorDiseaseTrends": "A list of indicators to monitor trends", "OtherGovernanceStructures": "Other governance structures", "DocumentedDetailsPlaceholder": "Identify where this is documented,add link if available and describe", "ExternalBodiesDetailsPlaceholder": "e.g. technical working groups or other external bodies who provide governance for surveillance", "TableFooterProportions": "Proportion of structures available", "TableFooterProportionsTooltip": "e.g 2/3= 67% (number of 'yes'/Total)", "SMCResponseDesc": "Identify and describe the health management structures that regularly review SMC surveillance data", "SMCMonitorDiseaseTrends": "A list of indicators to monitor trends", "SMCTableFooterProportionsTooltip": "e.g 2/3= 67% (number of 'yes'/Total)", "IPTIResponseDesc": "Note: Proportions of subnational level units/offices and/or service delivery points with these structures in place are determined though the survey", "ITPITableFooterProportionsTooltip": "e.g 2/3= 67% (number of 'yes'/Total)", "ResponseError": "Please select 'Yes' or 'No' to indicate whether all structures are 'In Place' to finalize the indicator", "MetNotMetTooltip": "If proportion of structures available is <br/> 100% = Met <br/> 33% or 67% = Partially met <br/> 0% = Not met", "GenomicResponseDesc": "Identify and describe the health management structures that regularly review genomic surveillance data", "ResponseDescText": "Identify and describe the health management structures that regularly review surveillance data", "LarvalResponseDesc": "Identify and describe the health management structures that regularly review larval source management surveillance data", "LarvalTableFooterProportionsTooltip": "e.g 2/3= 67% (number of 'yes'/Total)", "IRSResponseDesc": "Identify and describe the health management structures that regularly review IRS surveillance data", "IPTpResponseDesc": "Identify and describe the health management structures that regularly review IPTp surveillance data:", "DrugEfficacyResponseDesc": "Identify and describe the health management structures that regularly review drug efficacy surveillance data:", "ENTOResponseDesc": "Identify and describe the health management structures that regularly review entomology surveillance data:", "IPTIResponseDescIdentify": "Identify and describe the health management structures that regularly review IPTi surveillance data:", "ITNMassResponseDesc": "Identify and describe the health management structures that regularly review ITN mass campaign surveillance data:", "CommodityResponseDesc": "Identify and describe the health management structures that regularly review commodity tracking  data:", "ITNResponseDesc": "Identify and describe the health management structures that regularly review ITN surveillance data:"}, "Indicator_4_1_2": {"IdentifyStrategicPlanningDocuments": "Can you identify and describe the strategic planning documents available and whether they are available at all levels of the health system?", "DetailsPlaceholder": "Identify where this is documented,add link if available and describe e.g. national three or five-year strategic plan that includes malaria surveillance or surveillance standard operating procedures (SOPs)"}, "Indicator_4_1_3": {"DataQualityTargets": "Are there set data quality targets (data accuracy, completeness, and timeliness) for malaria surveillance in place?", "DetailsPlaceholder": "e.g. 80% of reports on time; 80% of  core variables complete, etc. "}, "Indicator_4_2_1": {"EvidenceInKeyDocuments": "Determine from evidence in key documents or from responses from informational interviews if each of the following are true or false:", "Questions": "Questions", "TrueFalse": "True/False", "OrganizationForUseOfInformation": "The {MoH/NMP/ other governance or organization} advocates for use of information at all levels of the health system", "OrganizationPromotesFeedback": "The {MoH/NMP/ other governance or organization} promotes feedback to and from surveillance staff", "OrganizationEmpowersSurveillanceStaff": "The {MoH/HI/NMP/ other governance or organization} empowers surveillance staff to ask questions, learn, and improve", "ActionTakenBasedOnData": "Action is taken based on data recorded/reported by surveillance staff", "DataUsefulToSurveillanceStaff": "Data recorded/ reported are useful to surveillance staff or the health facility/subnational level unit/office they work in", "SurveillanceTasksNotBurden": "Surveillance tasks are not a burden or do not distract from other duties those who are responsible for recording and reporting may have", "ResponsibilityOfHealthCareProviders": "Data recording and reporting are seen as the responsibility of health care providers", "StaffPaidOnTime": "Surveillance staff are adequately paid and on time", "ProvideDetailsPlaceholder": "How? (Provide details for 'True')", "HowPlaceholder": "How?", "WhatActionsPlaceholder": "What actions?", "ExplainReasonsPlaceholder": "Explain reasons for any burden:", "ExplainYesNoPlaceholder": "Explain why yes or no", "ExplainGapsPlaceholder": "Explain any gaps:"}, "Indicator_4_2_2": {"StaffPerformHighQuality": "What motivates malaria surveillance staff to perform high quality surveillance?", "StaffPerformHighQualityPlaceholder": "Consider: a sense of responsibility and accountability, recognition or rewards, the impact on the malaria burden or improving the malaria program, to drive evidence-based decision making. Summarize findings or responses here:", "CausesOfDemotivation": "What are causes of demotivation among malaria surveillance staff?", "CausesOfDemotivationPlaceholder": "Consider: tasks are too difficult or tedious, other tasks take priority, tasks are not the responsibility of staff conducting them, staff are not or not adequately compensated (late payments)."}, "Indicator_4_3_1": {"ResponseDesc": "Is there a supervision plan in  place for malaria surveillance?", "ResponseDesc1": "Describe the supervision processes in place for surveillance <br/> i.e. Who reviews and provides feedback on surveillance activities? How do service-delivery points receive feedback?", "Supervision": "Supervision", "FromNationalLevelToSubnationalLevel": "From national level to subnational level", "FromSubnationalLevelToServiceDeliveryLevel": "From subnational level to service delivery level", "DescriptionOfSupervisionPlan": "Description of supervision plan and processes", "LevelOfSupervision": "Level of supervision and management:", "SupervisionGuidelines": "Supervision guidelines", "SupervisionVisitsChecklists": "Checklists for supervision visits", "SupervisionVisitSchedule": "Supervision visit schedule", "ProportionOfSupervisonDocuments": "Proportion of supervison documents available at the national level", "ProportionOfSupervisonDocumentsTooltip": "e.g 2/3=67% (Calculation (# of available document 'Yes'/ Total number of documents) *100", "SupervisonPlanProcessesPlaceholder": "Describe the supervison plan and processes here. If no system is in place, describe reasons why not and record 'NA' in the rest of the cells below", "CentralDecentralizedPlaceholder": "e.g. central or decentralized ", "SMCResponseDesc": "Is there a supervision plan in  place for SMC?", "SMCRelevantDocuments": "Relevant documents detailing supervision processes", "SMCRelevantDocumentsPlaceholder": "Yes no, are these available: √ or × - Supervision guidelines √ or × - Checklists for supervision visits √ or × - Supervision visit schedule", "SMCRelevantDocumentsPlaceholder2": "e.g. supervision guidelines, checklists for supervision visits, supervision visit schedule", "SMCProportionOfSupervison": "Proportion of supervison documents available at the national level", "SMCProportionOfSupervisonPlaceholder": "e.g 2/3=67% (Calculation (# of available document 'Yes'/ Total number of documents) *100", "IPTIResponseDesc": "Is there a supervision plan in place for IPTi? ", "IPTITextboxLabel": "Proportion of supervison documents available at the national level", "IPTITextboxPlaceholder": "e.g 2/3=67% (Calculation (# of available document 'Yes'/ Total number of documents) *100", "MDAResponseDesc": "Is there a supervision plan in  place for MDA?", "ITNRoutineResponseDesc": "Is there a supervision plan in  place for ITNs? <br/> Describe the supervision processes in place for surveillance <br/> i.e. Who reviews and provides feedback on surveillance activities? How do service-delivery points receive feedback?", "IRSResponseDesc": "Is there a supervision plan in place for IRS surveillance?", "GenomicResponseDesc": "Is there a supervision plan in place for genomic surveillance?", "EntoResponseDesc": "Is there a supervision plan in  place for entomology surveillance ? <br/> Describe the supervision processes in place for surveillance <br/> i.e. Who reviews and provides feedback on surveillance activities? How do service-delivery points receive feedback?", "ITPIRelevantdocuments": "Relevant documents detailing supervision processes", "ITPIPlaceholderOne": "Yes no, are these available:", "ITPISupervision": " √ or × - Supervision guidelines", "ITPIChecklist": " √ or × - Checklists for supervision visits ", "ITPIVisit": " √ or × - Supervision visit schedule", "ITPIPlaceholderTwo": "e.g. supervision guidelines, checklists for supervision visits, supervision visit schedule", "DrugEfficacyResponseDesc": "Is there a supervision plan in place for drug efficacy?", "CommodityResponseDesc": "Is there a supervision plan in place for commodity tracking surveillance?", "LarvalResponseDesc": "Is there a supervision plan in place for larval source management?", "ResponseErrorText": "Please provide details for national to sub-national or sub-national to service delivery to finalize the indicator", "ResponseErrorRadio": "Please select 'Yes' or 'No' for all national to sub-national or sub-national to service delivery documents availability to finalize the indicator", "MetNotMetTooltip": "If proportion of supervision documents available is <br/> 100% = Met <br/> 33% or 67% = Partially met <br/> 0% = Not met", "IPTPResponseDesc": "Is there a supervision plan in place for IPTp surveillance?"}, "Indicator_4_4_1": {"ResponseDesc": "Indicate which of the follow surveillance tasks are covered in the most recent training at each level of the health system.", "ResponseDescDetails": "Enter details for training on malaria surveillance", "ResponseDescElimination": "Enter details for training on elimination surveillance", "ResponseDescBoth": "Enter details for training on both surveillance", "Training": "Training", "NationalLevel": "National Level", "SubnationalLevel": "Subnational Level", "ServiceDeliveryLevel": "Service Delivery Level", "DataCollection": "Data collection (recording patient information)", "DataReporting": "Data reporting (compiling malaria case reports)", "ConductingDataQualityReview": "Conducting data quality review of malaria case data", "ConductingDataAnalysis": "Conducting data analysis of malaria case data and data use", "PreparingDisseminationReports": "Preparing dissemination reports (such as bulletin, newsletters, feedback reports)", "Supervision": "Supervision", "CaseNotification": "Case notification", "CaseInvestigation": "Case investigation", "CaseClassification": "Case classification", "FociInvestigation": "Foci investigation", "FociClassification": "Foci classification", "QualityAssuranceOfLabData": "Quality assurance of lab data", "TrainingForMicroscopy": "Training for microscopy", "TrainingInPublicPrivateSectors": "Training occurs in both the public and private sectors", "Attendants": "Attendants", "FrequencyOfTraining": "Frequency of training", "DateOfLastTraining": "Date of last training", "ProportionOfRelevantStaff": "Proportion of relevant staff who have received training in the previous 3 years", "ProgramStaffPlaceholder": "e.g. all malaria program staff", "AnnuallyPlaceholder": "e.g. annually", "TrainingCarriedOut": "Has training been carried out ?", "ProportionOfRelevantStaffTooltip": "Number of respondents who have received surveillance training/ Total number of respondents who are responsible for malaria surveillance tasks", "SMCResponseDesc": "Indicate which of the follow surveillance tasks are covered in the most recent training at each level of the health system. Enter details for training on SMC surveillance", "SMCDataCollection": "Data collection", "SMCDataReporting": "Data reporting", "SMCConductingDataQualityReview": "Conducting data quality review", "SMCConductingDataAnalysis": "Conducting data analysis ", "IPTIResponseDesc": " Indicate which of the follow surveillance tasks are covered in the most recent training at each level of the health system. Enter details for training on IPTi surveillance", "MDAResponseDesc": "Indicate which of the follow surveillance tasks are covered in the most recent training at each level of the health system. Enter details for training on MDA surveillance", "MDAMonitoringAdverseEvents": "Monitoring of adverse events", "MDAMonitoringDrugResistance": "Monitoring of drug resistance", "ITNRoutineResponseDesc": "Indicate which of the follow surveillance tasks are covered in the most recent training at each level of the health system. Enter details for training on ITN surveillance", "IRSResponseDesc": "Indicate which of the follow surveillance tasks are covered in the most recent training at each level of the health system. Enter details for training on IRS surveillance", "GenomicResponseDesc": "Indicate which of the follow surveillance tasks are covered in the most recent training at each level of the health system. Enter details for training on genomic surveillance", "EndoResponseDesc": "Indicate which of the follow surveillance tasks are covered in the most recent training at each level of the health system. Enter details for training on entomology surveillance", "LarvalResponseDesc": "Indicate which of the follow surveillance tasks are covered in the most recent training at each level of the health system. Enter details for training on larval source management surveillance", "DrugEfficacyResponseDesc": "Indicate which of the follow surveillance tasks are covered in the most recent training at each level of the health system. Enter details for training on drug efficacy surveillance", "CommodityResponseDesc": "Indicate which of the follow surveillance tasks are covered in the most recent training at each level of the health system. Enter details for training on commodity tracking surveillance", "ResponseError": "Please select 'Yes' or 'No' for all national, sub-national and service delivery trainings to finalize the indicator", "MetNotMetTooltip": "Proportion of planned trainings that occurred in the previous year is <br/> 100% = Met <br/> 1%-99% = Partially met <br/> 0% = Not met <br/><br/> This should be assessed at each health sector level. <br/> If all three are met=Met <br/>If one or more are partially met=Partially met <br/>If one or more are not met=Not met", "IPTPDataCollection": "Data collection", "IPTPDataReporting": "Data reporting", "IPTPConductingDataQualityReview": "Conducting data quality review", "IPTPConductingDataAnalysis": "Conducting data analysis ", "IPTPResponseDesc": " Indicate which of the follow surveillance tasks are covered in the most recent training at each level of the health system. Enter details for training on IPTp surveillance", "PlannedTrainings": "Number of planned trainings in the previous year", "PreviousYearTrainings": "Number of trainings that occurred  in the previous year", "ProportionOfPlannedTrainings": "Proportion of planned trainings that occurred in the previous year", "ProportionOfPlannedTrainingsTooltip": "Number of trainings that occurred  in the previous year / Number of planned trainings in the previous year", "ResponseErrorForPreviousYear": "Please provide details of 'Number of planned trainings in the previous year' and 'Number of trainings that occurred in the previous year' for all to finalize the indicator", "DataCollectionHeading": "Data collection", "DataReportingHeading": "Data reporting", "ConductingDataQualityReviewHeading": "Conducting data quality review", "ConductingDataAnalysisHeading": "Conducting data analysis "}, "Indicator_4_4_2": {"ResponseDesc": "Describe any job-aids for malaria surveillance staff (e.g. poster or leaflet) provided to surveillance staff at each level of the health system.", "ResponseDesc1": "Determine or approximate the proportion of surveillance staff at each level of the health system who have access to a job-aid", "JobAids": "Job Aids", "NationalLevel": "National Level", "SubnationalLevel": "Subnational Level", "ServiceDeliveryLevel": "Service Delivery Level", "JobAidContent": "Job aid content:", "DescribeJobAids": "Describe job-aids here. If there are no job-aids, record 'NA' in each cell", "ProportionOfRelevantStaff": "Proportion of relevant staff who have access to a job aid (%)", "ProportionOfRelevantStaffPlaceholder": "Numerator (Number of 'Yes')/Denominator <br/> (Number of rows (number of 'Yes' from L11:L26)", "DescribeTheEstimation": "If this is not documented, estimate and describe how the estimation was made"}, "Indicator_4_4_3": {"ResponseDesc": "In your opinion or experience, which tasks do surveillance staff find difficult to complete in the below tasks and why?", "Tasks": "Tasks", "DifficultyInCompletion": "Difficulty in completion", "DescriptionOfWhy": "Description of why", "RecordMalariaData": "Record malaria data in source documents (patient register)", "ReportMalariaDataToDistrict": "Report malaria data to the district", "DataQualityChecks": "Perform data quality checks", "CalculateMalariaIndicators": "Calculate malaria indicators", "DataVisualsOfMalariaIndicators": "Prepare data visuals of malaria indicators", "InterpretMalariaData": "Interpret malaria data", "CaseNotification": "Case notification", "CaseInvestigation": "Case investigation", "CaseClassification": "Case classification", "FocusInvestigation": "Focus investigation", "FocusClassification": "Focus classification", "FocusResponse": "Focus response", "MakingDecisions": "Use information for problem solving or making decisions", "PersonInterviewedJobRole": "Job role of the person being interviewed", "ResponseError": "Please select 'Yes' or 'No' for all 'Difficulty in completion' to finalize the indicator"}}}