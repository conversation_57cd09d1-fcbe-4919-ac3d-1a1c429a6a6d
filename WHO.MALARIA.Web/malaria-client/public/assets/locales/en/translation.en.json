﻿{
  "app_title": "Title",
  "language": "Language",
  "welcome_message": "Welcome",
  "days_since_release": "it's been {{number_of_days}} days since this video was released",
  "GreetingMessage": "Hope you are having a great day!",
  "Menus": {
    "Dashboard": "Dashboard",
    "About": "About",
    "Contact": "Contact",
    "Controls": "Controls",
    "Home": "Home",
    "Login": "Login",
    "Logout": "Logout",
    "UserManagement": " User Management",
    "MyProfile": "My Profile",
    "Analytics": "Analytics",
    "HealthFacilityManagement": "Health Facility Management",
    "Setup": "Setup",
    "UploadDocument": "Upload Document"
  },
  "Errors": {
    "Oops": "Oops",
    "PageNotFoundMessage": "Sorry, we're unable to find the page you are looking for.",
    "UnAuthorizedAccessMessage": "You are not authorized to view this page",
    "MandatoryField": "Field is mandatory",
    "InvalidEmail": "Email is not valid",
    "DeactivatedUserMessage": "Your account has been deactivated. Please contact administrator (<EMAIL>).",
    "ValueBetweenOneToHundred": "Please enter value from 1 to 100",
    "ValueBetweenZeroToHundred": "Please enter value from 0 to 100",
    "DenominatorGreaterThanZero": "Denominator cannot be '0'. Please enter value greater than '0'",
    "ValueBetweenZeroToFour": "Please input number between 0 to 4 to finalize indicator",
    "YearBetween2010TillCurrentYear": "Year should be between 2010 and 2021",
    "YearBetween2010ToCurrentYear": "Year should be between 2010 and Current year",
    "SomethingWentWrong": "Something went wrong!",
    "ValueBetweenOneToTwelve": "Please input number between 0 to 12",
    "ValueBetweenDenominatorRange": "Please input number between selected denominator range",
    "ProportionValueMessage": "Proportion value should be from 0 to 100",
    "MetNotMetSelect": "Please select an option from 'Met, Partially Met or Not Met'",
    "AtLeastOnePublicHealthSystemDescendentNotSelected": "At least one facility within the Public sector should be selected as 'Yes'",
    "SelectParentPublicHealthSector": "Please select 'Yes' for the Public health sector as decedent health systems are marked as 'Yes' else select 'No' for all descendent health systems",
    "AtLeastOnePrivateFormalDescendentHealthSystemMustBeSelected": "At least one facility within the Private formal sector should be selected as 'Yes'",
    "SelectParentPrivateFormalHealthSector": "Please select 'Yes' for the Private formal health sector as decedent health systems are marked as 'Yes' else select 'No' for all descendent health systems",
    "InActivatedUserMessage": "You don't have any country access. Please contact administrator (<EMAIL>).",
    "DifferentOtherDataSystemName": "Other data system name should be different.",
    "UserProfileAccessCountryMessage": "You don't have an access to any country. Do you want to request an access to countries?",
    "UnregisteredUserMessage": "You're an unregistered user, please register yourself by clicking on the register button on the landing screen.",
    "ValueBetweenOneToTwelveDesc": "If there are 10 regions expected to produce a report every month then the denominator should be 120 (10 x 12). ",
    "PleaseSelectSrategies": "Please select strategies",
    "AssessmentIdShouldNotBeEmpty": "AssessmentId should not be empty.",
    "InvalidFile": "Please select valid file",
    "InvalidDate": "Please select valid date",
    "ZeroNotAllowed": "Value '0' is not allowed. Please enter a value greater than 0"
  },
  "NotificationMessages": {
    "AuthenticationRedirection": "Redirecting for Authentication...",
    "Redirecting": "Redirecting...",
    "MaxRequestSize": "The request size limit is crossed. Please do not upload the file(s) whose total size is greater than 26 MB"
  },
  "app": {
    "SuperManagersTitle": "Malaria Toolkit - Super Managers",
    "UsersTitle": "Malaria Toolkit - Users",
    "AssessmentsTitle": "Malaria Toolkit - Assessments",
    "ScopeDefinitionTitle": "Malaria Toolkit - Define Scope",
    "LandingPageTitle": "Welcome to Malaria Surveillance Assessment Toolkit",
    "NotFoundTitle": "Page Not Found",
    "AssessmentStrategySelectionTitle": "Malaria Toolkit - Strategy Selection",
    "IndicatorSelectionTitle": "Malaria Toolkit - Indicator Selection",
    "DashboardTitle": "Malaria Toolkit - Dashboard",
    "DeskReviewTitle": "Malaria Toolkit - Desk Review",
    "DQATitle": "Malaria Toolkit - DQA",
    "QuestionBankTitle": "Malaria Toolkit - Question Bank",
    "UploadDiagramTitle": "Malaria Toolkit - Upload Diagram",
    "AcceptInvitationTitle": "Malaria Toolkit - Accept Invitation",
    "MalariaAssessmentToolkit": "Malaria Surveillance Assessment Toolkit",
    "CountryAccessRequestTitle": "Malaria Toolkit - Country Access Request For WHO User",
    "HealthFacilitiesPageTitle": "Malaria Toolkit - Health Facilities",
    "ScoreCardTitle": "Malaria Toolkit - Score Card",
    "GenerateReport": "Malaria Toolkit - Generate Report",
    "DataAnalysis": "Malaria Toolkit - Data Analysis",
    "DQASummary": "Malaria Toolkit - DQA Summary",
    "SurveyResultTitle": "Malaria Toolkit - Survey Result",
    "UploadDocumentTitle": "Malaria Toolkit - Upload Document",
    "DeactivatedWHOUser": "Malaria Toolkit - Deactivated WHO User",
    "DeactivatedUser": "Malaria Toolkit - Deactivated User",
    "InactivatedUser": "Malaria Toolkit - Inactive User",
    "UnregisteredUser": "Malaria Toolkit - Unregistered User",
    "GoogleAnalytics": "Malaria Toolkit - Google Analytics",
    "DqaFileName": "Service_Level_DQA",
    "DeskDqaFileName": "Desk_Level_DQA",
    "DQAEliminationToolReportFileName": "DQA_Elimination_Tool_Report",
    "DQADeskLevelToolReportFileName": "DQA_Desk_Level_Tool_Report",
    "DQAEliminationFileName": "DQA_Elimination"
  },
  "Common": {
    "MonthDateYear": "mm/dd/yyyy",
    "YearMonthDate": "yyyy/mm/dd",
    "MonthYear": "MMM/yyy",
    "MonthDate": "MMM/dd",
    "Active": "Active",
    "Inactive": "InActive",
    "Add": "Add",
    "Cancel": "Cancel",
    "Save": "Save",
    "Grant": "Grant",
    "Edit": "Edit",
    "Viewer": "Viewer",
    "Manager": "Manager",
    "Approve": "Approve",
    "Reject": "Reject",
    "StartDate": "Start Date",
    "EndDate": "Expected End Date",
    "InviteUser": "INVITE AND ASSIGN",
    "Assign": "ASSIGN",
    "ShowData": "Show Data",
    "Yes": "Yes",
    "No": "No",
    "SaveAndContinue": "Save & Continue",
    "GoBack": "Go Back",
    "Update": "Update",
    "Invite": "Invite",
    "InviteAssign": "Invite and Assign New User",
    "AddInviteNewUser": "Add New User (Non-WHO)",
    "InviteAssignTooltip": "This option is to invite a user who is not a registered WHO user (WHO active directory)",
    "AssingExist": "Assign Existing User",
    "AddAssignUser": "Add New User (WHO)",
    "AssingExistTooltip": "This option is to invite a user who is a registered WHO user (WHO active directory)",
    "Next": "Next",
    "Loading": "Loading...",
    "NoPendingRequest": "Great! You don't have any pending request.",
    "Previous": "Previous",
    "DownloadAll": "Download All",
    "Countries": "Countries",
    "Finalize": "Finalize",
    "ReFinalize": "Re-Finalize",
    "Year": "Year",
    "Line": "Line",
    "ReasonForChanges": "Reasons for changes observed over time",
    "ReasonForChangesByRegion": "Reasons for changes observed by region",
    "Horizontal": "Horizontal",
    "Vertical": "vertical",
    "GenerateGraph": "Generate Graph",
    "View": "View",
    "PleaseSpecify": "Please Specify",
    "From": "From",
    "To": "To",
    "SelectYear": "Select Year",
    "ConfirmationTitle": "Confirmation",
    "Ok": "Ok",
    "Sex": "Sex",
    "Age": "Age",
    "Diagnosis": "Diagnosis",
    "Total": "Total",
    "InProgress": "In Progress",
    "Completed": "Completed",
    "PleaseUploadFiles": "Please upload files",
    "NoFilesToPreview": "No files to preview",
    "Upload": "Upload",
    "GeographicRegionsNote": "The regions used in WHO (and on this site) may differ from the geographic regions of the world.",
    "MaleriaStrategyControl": "Choose if you would like to assess surveillance for malaria control interventions and strategies",
    "SelectCaseServillance": "Surveillance of malaria cases by transmission setting",
    "CaseStrategy": "Case Strategy",
    "BurdenReduction": "Burden reduction",
    "Elimination": "Elimination",
    "Both": "Both",
    "NotApplicable": "N/A",
    "MaleriaStrategyControlTooltip": "This is a high-level assessment of routine and/or non-routine surveillance systems for malaria control interventions and strategies to understand which data are collected and how, and if data are integrated and used along with malaria case surveillance data",
    "ConfirmationToGoBack": "Changes will not be reflected if you go back without Save/Finalize. Do you want to Save/Finalize the changes?",
    "InvitationNotAccepted": "Invitation not accepted",
    "InvitationSent": "Invitation Sent",
    "Export": "Export",
    "ExportAll": "Export All",
    "Indicator": "Indicator",
    "ConfirmationViewerToSuperManager": "Do you want to change the role of this user as super manager?",
    "TransmissionSetting": "Transmission Setting",
    "Level": "Level",
    "National": "National",
    "Regional": "Regional",
    "District": "District",
    "Average": "Average Likert Score",
    "AverageVisits": "Average number of visits",
    "AverageScore": "Average Score",
    "YearOfData": "Year of Assessment",
    "DownloadTemplate": "Download Template",
    "Report": "Publish Results",
    "Met": "Met",
    "NotMet": "Not Met",
    "PartiallyMet": "Partially Met",
    "NotAssessed": "Not Assessed",
    "disputed":"Disputed",
    "Global": "Global",
    "Country": "Country",
    "NoData": "No Data",
    "Request": "Request",
    "UploadDocument": "Upload Document",
    "SelectLanguage": "Select Language",
    "English": "English",
    "French": "French",
    "Reset": "Reset",
    "LastUploadedFile": "Last Uploaded File",
    "Select": "Select",
    "HowToAssess": "How to assess",
    "WhatYouNeed": "What you need",
    "Interview": "Interview",
    "Public": "Public",
    "Private": "Private",
    "Community": "Community",
    "Burden reduction": "Burden reduction",
    "IPTp": "IPTp",
    "IPTi": "IPTi",
    "SMC": "SMC",
    "MDA": "MDA",
    "ITNs-Routine": "ITNs-Routine",
    "ITNs-mass campaign": "ITNs-mass campaign",
    "IRS": "IRS",
    "Larval Source Management": "Larval Source Management",
    "Commodity tracking": "Commodity tracking",
    "Entomology": "Entomology",
    "Drug efficacy": "Drug efficacy",
    "Genomics": "Genomics",
    "Reviewer": "Reviewer",
    "Editor": "Editor",
    "SendToWHO": "Forward to WHO"
  },
  "Analytics": {
    "AnalyticsHeading": "Analytics",
    "EventAction": "Event action",
    "EventCategory": "Event category",
    "UserType": "User type",
    "PageTitle": "Page title",
    "City": "City",
    "NoAnalyticsDataFound": "No any analytics data found.",
    "ActiveUsersCount": "Active users",
    "Date": "Date"
  },
  "UploadDocument": {
    "UploadDocumentMessage": "Upload documents for the Malaria Surveillance Assessment Toolkit",
    "UploadDocumentConfirmationMessage": "Do you want to upload documents for the Malaria Surveillance Assessment Toolkit?",
    "FileUploadSize": "Max. Size Limit (50 mb)",
    "Message": {
      "UploadDocumentsSuccessMessage": "Documents has been uploaded successfully.",
      "UploadDocumentsErrorMessage": "Unable to upload the documents"
    }
  },
  "UserManagement": {
    "Name": "Name",
    "Email": "Email",
    "OrganizationName": "Organization Name",
    "ChooseCountry": "Choose Country to be assigned",
    "AddSuperManager": "ASSIGN NEW SUPERMANAGER",
    "AddNewUser": "Add new user",
    "Role": "Role",
    "RegisteredNewUser": "Registered (New User)",
    "Supermanager": "Supermanager",
    "Super Manager": "Super Manager",
    "WhoAdmin": "WHO Admin",
    "Viewer": "Viewer",
    "Manager": "Manager",
    "AllCountriesToWHOAdmin": "All countries are assigned to the WHO Admin role",
    "Activate": "Activate",
    "DeActivate": "DeActivate",
    "ChangeToSuperManager": "Change to Super Manager",
    "SuperManagers": "Super Managers",
    "AllUsers": "All Users",
    "PendingRequests": "Pending Requests",
    "PendingRequestTitle": "Pending Requests",
    "ProvideCommentsAndReasons": "Provide Comments/Reasons",
    "Register": "Register",
    "Profile": "Profile",
    "CountryAccessRequired": "Country Access Required",
    "CountryAccessTooltip": "Select the country for which you require access",
    "RegisterGreetingInstruction": "Kindly fill in the information below to get registered.",
    "RegisterGreetingNote": "If you already have a Microsoft or Gmail account, you can use these email addresses to create an account. All other email accounts will require the use of a One-Time Passcode sent to your email account for login. One-time passcodes are valid for 30 minutes. After 30 minutes, that specific one-time passcode is no longer valid, and you must request a new one.",
    "ProfileGreetingInstruction": "Kindly fill in the mandatory information below to get started. We recommend that you keep your contact information and other details updated.",
    "CountryAccessRequests": "Country Access Requests",
    "UserActivationRequests": "User Activation Requests",
    "EditUser": "Edit User",
    "CountryAccessGranted": "Country Access Granted",
    "CountryAccessRequested": "Country Access Request",
    "CountriesRequested": "Countries Requested",
    "ResendInvitation": "Resend Invitation",
    "GridColumn": {
      "Name": "Name",
      "Email": "Email",
      "OrganizationName": "Organization Name",
      "Country": "Country",
      "Status": "Status",
      "Role": "Role",
      "Comment": "Comment"
    },
    "Reason": "Reason",
    "ProvideReason": "Please Provide the reason",
    "SelectReason": "Please select reason",
    "RejectionReasons": {
      "UserIdentifier": "We are unable to identify you",
      "UnIdentifyUser": "Your are an unidentified user",
      "OrganizationalAccessDenied": "We cannot provide access to members of your organization",
      "OrganizationalCredentialNotVerified": "We are not able to verify your credentials with your organization",
      "Other": "Other"
    },
    "Message": {
      "UserRegistrationSubmitted": "Your request has been submitted. Respective super manager will take the action",
      "CreateSuperManagerSuccess": "Super manager is successfully created.",
      "UpdateUserSuccess": "User edited successfully",
      "UserStatusChangeSuccess": "User status changed successfully",
      "UserInvitationSuccess": "User invitation is successfully sent.",
      "UserUpdatedSuccess": "User updated successfully.",
      "UserCountryAccessRequest": "User country access request is successfully sent.",
      "ApproveUserCountryAccessRequest": "User country access request is successfully approved.",
      "RejectUserCountryAccessRequest": "User country access request is rejected.",
      "InvalidInvitationLink": "The invitation link is not valid!",
      "CreateWHOAdminSuccessMessage": "WHO Admin is successfully created.",
      "WhoUserAddedAsViewer": "Please note that your country/countries request has been submitted to respective Super Manager(s)",
      "UserTypeChangeSuccess": "Super manager role assigned successfully.",
      "UserRejectionSuccess": "User rejected successfully.",
      "ForwardToWhoAdminUserCountryAccessRequest": "User country access request is forwaded to WHO admin."
    }
  },
  "Dashboard": {
    "IndicatorDashboardSlider": "Indicator Dashboard",
    "RegionalDashboardSlider": "WHO Regional Summary",
    "ObjectiveDashboardSlider": "Objective Map",
    "RegionalDashboardNoData": "No country from this region has published the assessment.",
    "CountryDashboardNoData": "There is no assessment published for this country.",
    "NoPublishedData": "There is no assessment published.",
    "NoteForScoreOnIndicatorDashboard": "Upper triangle represents 'Survey Result' while lower triangle represents 'Desk review and DQA Result' for indicators, if captured for both.",
    "SurveyScore": "Survey Result",
    "DeskReviewScore": "Desk review and DQA Result"
  },
  "Assessment": {
    "AddNewAssessmentTitle": "Add New Assessment",
    "UpdateAssessmentTitle": "Update Assessment",
    "Reviewers": "Reviewers",
    "Editors": "Editors",
    "Manager": "Manager",
    "AssignUserRole": "Assign User Role",
    "Assessments": "Assessments",
    "AddAssessment": "Add Assessment",
    "ViewAssessments": "View Assessments",
    "AllAssessments": "All Assessments",
    "EditorsTooltip": "Select the editors from the list of country users",
    "ReviewersTooltip": "Select the reviewers from the list of country users",
    "ScopeDefinitionTitle": "Define Scope",
    "DataCollectionTitle": "Collect Data",
    "DataAnalysisTitle": "Data Analysis",
    "ReportGenerationTitle": "Generate score card",
    "ExpectedEndDate": "Expected End Date",
    "AssessmentRole": "Assessment Role",
    "SameUserDifferentRole": "You cannot assign same user in two different assessment roles",
    "ScopeDefinition": {
      "IndicatorLoadingMessage": "Please wait while we are loading Objectives and Sub-Objectives",
      "SelectIndicatorsApproach": "Select assessment approach and/or indicators to be assessed.",
      "IndicatorNoRecordFound": "Unable to render the view because of some failing condition.",
      "SubObjectiveNoRecordFound": "No Sub-Objective Found",
      "StrategySelection": "Strategy Selection",
      "IndicatorSelection": "Indicator Selection",
      "Finalize": "Finalize",
      "FinalizeDialogTitle": "Confirmation",
      "FinalizeDialogContent": "Please 'Save' changes before finalizing them. You will not be able to modify the assessment after it is finalized. Do you want to finalize the assessment?",
      "FinalizeUserMessage": "Finalized assessment can be viewed but cannot be modified.",
      "AssessmentApproachDialogTitle": "Asessment Approach Confirmation",
      "AssessmentApproachComphrehensiveDialogMessage": "Comprehensive assessments must have all indicators selected. Please either select all indicators or change the approach to a tailored assessment. Would you like to change the approach to a tailored assessment?",
      "AssessmentApproachRapidDialogMessage": "You have selected a rapid assessment but also optional indicators. Please either de-select optional indicators to continue with a rapid assessment or change the approach to a tailored assessment. Would you like to change the approach to a tailored assessment?",
      "AssessmentApproachTailoeredDialogMessage": "You have selected only priority indicators. Please select rapid assessment or at least one optional indicator if you wish to continue with a tailored assessment. Would you like to continue with a tailored assessment?",
      "AssessmentApproachRapidToComprehensiveDialogMessage": "You have selected a tailored assessment but also all of the indicators. Please either de-select optional indicators to continue with a tailored assessment or change the approach to a comprehensive assessment. Would you like to change the approach to a comprehensive assessment?",
      "RapidApproach": "Rapid assessment",
      "TailoredApproach": "Tailored (Country should make the selection for each optional indicator)",
      "ComprehensiveApproach": "Comprehensive (All indicators selected)",
      "StrategiessavedMessage": "Strategies successfully saved"
    },
    "GridColumn": {
      "Type": "Type"
    },
    "DataCollection": {
      "DeskReview": "Desk Review",
      "DQA": "Data Quality Assessment",
      "QuestionBank": "Question Bank",
      "ScoreCard": "Score Card",
      "SurveyResult": "Survey Result",
      "NoStrategySelectedMessage": "Please select Strategy to populate indicators.",
      "MissingAssessmentSelection": "Please select and assessment to view strategies.",
      "GridColumn": {
        "Assess": "Assess"
      },
      "DeskReviewDiagram": {
        "Diagram": "Diagram",
        "UploadDiagram": "Upload Diagram",
        "ReUpload": "Re-Upload",
        "Objective_3_UploadDiagramWarning": "This objective shall not be completed until diagrams are uploaded and finalized.",
        "Subobjective_2_2_UploadDiagramWarning": "This sub-objective shall not be completed until diagrams are uploaded and finalized.",
        "ExampleDiagrams": "Example Diagrams",
        "UploadedDiagrams": "Uploaded Diagrams",
        "Objective2UploadDesc": "Create and upload a data flow diagram. Please see example diagrams to help you to develop something similar for your country. It is helpful to include a diagram of the current situation and a separate diagram for any future plans/changes.",
        "Objective2UploadNote": "You can use Power Point or any application of your choice and upload a JPEG/PNG. <br/>Maximum 2 diagrams can be uploaded.",
        "UploadDesc": "Create and upload a data flow diagram using the template below as an example.",
        "UploadNote": "You can use Power Point or any application of your choice and upload a JPEG/PNG.",
        "UploadDiagramInfo": "There are no diagrams uploaded yet. <br/> Kindly refer to the example diagrams and create your own diagrams and upload.",
        "UploadTextboxSummarize": "Summarize the flow of data for malaria surveillance depicted in the diagram. Highlight strengths as well as any bottlenecks or challenges",
        "UploadTextboxSummarizePlaceholder": " e.g. consider adequacy of staff, trainings, availability of tools, time to complete reports for all notifiable diseases, clarity of guidelines, etc.",
        "UploadTextboxNote": "Note any planned changes or improvements to malaria surveillance processes already in the pipe-line:",
        "UploadTextboxProcess": "What would the process for modifying recording and reporting forms/ tools be? Consider all forms (case surveillance, case investigation, notification, etc.)",
        "DescribeProcessTools": "Insert text to describe process –tools used, personnel and frequency",
        "SummaryDataFlow": "Summary of data flow",
        "StrengthsChallenges": "Strengths and Challenges",
        "DataRecording": "Data recording",
        "DataReporting": "Data reporting",
        "PlannedChanges": "Planned changes",
        "DescribeSummaryProcessTools": "Insert text to summarize the flow of data for malaria surveillance depicted in the diagram- direction of data flow, personnel involved, frequency, supervision",
        "DescribeStrengthsChallenges": "Insert text to highlight strengths, as well as any bottlenecks or challenges (e.g. consider adequacy of staff, trainings, availability of tools, time to complete reports for all notifiable diseases, clarity of guidelines, etc.)",
        "DescribePlannedChanges": "Note any planned changes or improvements to malaria surveillance processes already in the pipe-line",
        "howToAssess": "- Use the information collected from assessing the indicators to develop a data flow diagram for malaria surveillance data. For each step in the data flow process, consider all malaria surveillance activities (e.g. case surveillance reporting, notification, investigation, and foci). Tools and procedures for all should be included in the diagram including frequency of recording and reporting and supervision.<br/> - During informational interviews, the draft versions of the diagram may be shared to validate with key informants. During site visits additional information should be gathered about the data flow process and this can be used to verify or modify the diagram as necessary.",
        "whatYouNeed": "<span class='icon icon-librarybooks' aria-hidden='true'>All forms/registers for recording malaria data including OPD, IPD, lab, community, public and private, both electronic and paper at all levels of the health system, data flow diagrams Interview</span> ",
        "UploadDiagramCompleted": "Upload Diagram Completed",
        "TechnicalProcess": "TECHNICAL AND PROCESSES",
        "InformationSystem": "INFORMATION SYSTEMS",
        "HMIS": "HMIS",
        "HMIS_FullRepository": "HMIS_FullRepository",
        "NationalMalariaRepository": "NationalMalariaRepository",
        "HMIS_Only": "HMIS_Only",
        "HMIS_FullRepository_CorePrinciples": "HMIS_FullRepository_CorePrinciples",
        "HMIS_HealthManagementInformationSystem": "HMIS_HealthManagementInformationSystem",
        "KeyDataSystemComponent": "KeyDataSystemComponent"
      },
      "DeskReviewObjetive2Diagram": {
        "howToAssess": "<div><span> Use details collected on information systems to develop an Information Systems Diagram showing the following systems below and how they are or are not integrated, health sector coverage (public, private, community) and which health system levels have access (national, region, district, health facility).</span> <br/>  - The primary information system for collecting malaria case surveillance data <br> - Any other systems that exist for collecting malaria case surveillance data  <br> - Any other systems used to collect data for other malaria control strategies <br/> - Information systems that collect aggregate or case-based data (in countries where both types of system exist e.g countries with sub-national elimination) <br/> <span>Please include all systems in one diagram if possible, however it may make sense to separate them if they systems are not integrated or if multiple systems over complicate the diagram. If you are carrying out surveillance assessments on other malaria control strategies you may wish to wait until you gather the information from these assessments before generating the diagram. </span> <br/> The option to upload two diagrams allows for <br/> - Separate information systems to be displayed on separate diagrams if necessary (e.g aggregate vs case based or malaria case surveillance vs vector control surveillance) </br> - The current situation and planned future improvements in system integration</div>",
        "whatYouNeed": "<span class='icon icon-librarybooks' aria-hidden='true'>Surveillance documents and systems documents</span> <br/> <span class='icon icon-person' aria-hidden='true'>Interview</span><br/><span class='icon icon-storage' aria-hidden='true'>If possible view the systems that exist</span>"
      },
      "DataQualityAssessment": {
        "DeskLevelDelivery": "Desk level",
        "ServiceLevelDelivery": "Service delivery level",
        "TemplateGeneration": "Template Generation",
        "DataAnalysis": "Data Analysis",
        "Priority": "Priority",
        "PriorityTabDesc": "It is recommended that all priority variables should be included if data are collected",
        "OptionalTabDesc": "These variables can be included if they are both relevant to the country context and data are routinely collected",
        "Optional": "Optional",
        "DataSystem1": "Data System 1",
        "DataSystem2": "Data System 2",
        "GenerateTemplate": "Generate Template",
        "FinalizeTemplate": "Finalize Template",
        "UploadData": "Upload Data",
        "HMISDHIS2": "HMIS/ DHIS 2",
        "IDSR": "IDSR",
        "Laboratory": "Laboratory",
        "VitalRegistration": "Vital registration",
        "Others": "Others",
        "Variables": "Variables",
        "Concordance": "Concordance",
        "SelectConcordance": "Select Concordance",
        "SelectConcordanceTooltip": "These variables should be selected for comparison between two different data systems. It is important that the definitions, time period and geography are the same for the variables that will be compared.",
        "SrNo": "Sr No.",
        "Indicator": "Indicator",
        "SelectDataSystems": "Select Data Systems",
        "SelectDataSystemsTooltip": "Select the data systems that capture malaria surveillance data for the DQA",
        "DataSystems1Tooltip": "This should be the primary data system that captures malaria surveillance data. All DQA indicators will be assessed on data extracted from this system.",
        "DataSystems2Tooltip": "This should be a second data system that captures all or some of the core variables. Data extracted from this system will be compared with data system 1 (concordance). If no second system exists or you do not have access to the data then please leave this blank. ",
        "SelectVariablesConcordance": "Select variables to be included in the assessment of data quality indicators and those to assess for concordance only",
        "SelectCoreVariables": "Select Core Variables",
        "SelectCoreVariablesTooltip": "Please select the core variables for that you would like to assess for concordance between HMIS/Aggregate report data and Source data (e.g patient register)",
        "SourceName": "Source Name",
        "SelectTimePeriod": "Select Time Period",
        "SelectTimePeriodTooltip": "Select the time period for which you want to assess data quality. It is recommended that you assess at least one quarter (3 months) for the most recent year of data. This may span two years (e.g November 2023-January 2024). Depending on the burden of malaria you may wish to only assess 1 month of data (high burden) or 12 months of data (low burden).",
        "SelectRegisterType": "Select Register Type",
        "SelectRegisterTypeTooltip": "Please select the registers for which you would like to assess the following variables for completeness; Sex (male/female), Age/Age group and Diagnosis.",
        "ValidationPeriodStartDate": "Validation Period Start Date",
        "ServiceDQA": "Service DQA",
        "DLDQAFinalizeDialogContent": "Do you want to finalize the current desk level DQA?",
        "DQAEliminationQualityEstimateMessage": "Please complete the summary of national data quality results from the elimination DQA assessment.",
        "SLDQAFinalizeDialogContent": "Do you want to finalize service level DQA?",
        "SLDQAReFinalizeDialogContent": "Do you want to re-finalize service level DQA?",
        "OutpatientRegister": "Outpatient register",
        "InpatientRegister": "Inpatient register",
        "LabRegister": "Lab register",
        "ConcordanceCoreVariablePercent": "% months concordance for each core variable",
        "ErrorDataSources": "Error in data sources**",
        "NationalDataQualityEstimates": "Indicators",
        "NationalLevelResults": "National level results (%)",
        "NationalLevelTarget": "National level target (%, optional)",
        "Desc1_2_14": "1.2.14 Reasons for observed data quality results",
        "Placeholder1.2.14": "Add text here",
        "Desc1.2.11": "1.2.11 Completeness of core variables within registers",
        "Desc1.2.12": "1.2.12 Concordance of core variables between registers and aggregated reports",
        "Desc1.2.13": "1.2.13 Error in data sources (The value difference for each core variable between data source one (D1) and data source 2 (D2))",
        "SummaryResults": "Summary Results",
        "TemplateUploadMessage": "Please wait... While we are processing the uploaded file",
        "SummaryDataNotAvailable": "Please upload Service Level DQA template to view results",
        "FileUploadSize": "Max. Size Limit (25 mb)",
        "FileUploadDetailText": "Please upload the final surveillance assessment report, the technical brief and a debrief presentation",
        "Max10MBFileSizeLimit": "Max. Size Limit (10 mb)",
        "RegisterTypeError": "Select atleast one Register Type",
        "DataSystemsError": "Please select atleast one Data Source from below Data Systems",
        "SLVariableSelectError": "Select atleast one Core Variable",
        "VariableConcordanceError": "Please select at least one variable for concordance.",
        "Result": "Result",
        "LegendTextCoreVariables": "*Color key for data with percentage: <span class='txt-red'>red</span> cells indicate percentage is less than 80% and greater than 100% ; <span class='txt-yellow'>yellow</span> cells indicate percentage is between 80%-95% and <span class='txt-green'>green</span> cells indicate percentage is greater than 95%",
        "LegendTextErrorDataSource": "**Color key for error in data sources for each core variable: <span class='txt-red'>red</span> cells indicate underreporting and <span class='txt-blue'>blue</span> cells indicate overreporting of data into the HMIS",
        "SummaryNationalDataQualityEstimates": "Summary of national data quality results",
        "SummaryPercentage": "% or indicator met (Yes/No)",
        "ReportCompleteness": "Completeness of reporting",
        "ReportTimeliness": "Timeliness of reporting",
        "CaseInvestigationReportsCompleteness": "Completeness of case investigation reports",
        "CaseNotificationReportsTimeliness": "Timeliness of case notification reports",
        "CaseInvestigationReportsTimeliness": "Timeliness of case investigation reports",
        "FociInvestigationReportsTimeliness": "Timeliness of foci investigation reports",
        "CoreVariableCompletenessWithinReport": "Completeness of core variables within reports",
        "ConsistencyBetweenCoreVariables": "Consistency between core variables",
        "ConsistencyOvertimeCoreIndicators": "Consistency over time for core indicators*",
        "CoreIndicators": "Core Indicators",
        "ConsistentTrend": "Consistent trend (Yes/No)",
        "ConfirmMalariaCasesNotified": "Number of confirmed malaria cases notified",
        "ConfirmMalariaCasesInvestigated": "Number of confirmed malaria cases investigated",
        "ConfirmMalariaCasesClassified": "Number of confirmed malaria cases classified",
        "ConfirmMalariaCasesClassifiedAsLocal": "Number of confirmed malaria cases classified as local (Indigenous + Introduced)",
        "ConfirmMalariaCasesClassifiedAsIndigenous": "Number of confirmed malaria cases classified as indigenous",
        "ConfirmMalariaCasesClassifiedAsIntroduced": "Number of confirmed malaria cases classified as introduced",
        "ConfirmMalariaCasesClassifiedAsImported": "Number of confirmed malaria cases classified as imported",
        "MalariaCasesDueToPF": "Number of malaria cases due to P.f",
        "MalariaCasesDueToPK": "Number of malaria cases due to P.k",
        "MalariaCasesDueToPM": "Number of malaria cases due to P.m",
        "MalariaCasesDueToPO": "Number of malaria cases due to P.o",
        "MalariaCasesDueToPV": "Number of malaria cases due to P.v",
        "KeyVariableConcordanceBtwTwoReportingSystem": "Concordance of key variables between two reporting systems",
        "CoreVariableCompletenessWithinRegister": "Completeness of core variables within registers",
        "CoreVariableConcordanceBtwRegister": "Concordance of core variables within registers",
        "EliminationDesc1.2.14": "Reasons for observed data quality results",
        "EliminationPlaceholder1.2.14": "The country should provide a description here of challenges and good practices and any reasons for poor data quality results, if known.",
        "Footnote1.2.9": "Yes if all of the trends are consistent for each core indicator or inconsistencies can be explained and are not due to data quality issues",
        "CoreIndicatorsError": "Please select 'Yes' or 'No' for 'Consistent trend' for all Core Indicators",
        "DeskLevelDeliveryBurden": "Desk Level - Burden Reduction",
        "ServiceLevelDeliveryBurden": "Service Delivery Level - Burden Reduction",
        "DQAElimination": "DQA - Elimination",
        "DQAEliminationDescription": "This DQA desk level and service delivery level tool can be used to assess data quality indicators, in elimination settings at national, province, district and health facility levels. <br/> This tool is designed to use existing routinely collected case-based surveillance data stored at the national level. <br/> At the desk level indicators are assessed directly on data extracted from the primary malaria surveillance system. Aggregate numbers of cases can also be compared between the MIS and other systems capturing malaria cases. <br/> At the service delivery level, data extracted from the primary malaria surveillance system can be compared with source data from patient registers/case investigation forms at the province, district, treatment health facility and laboratory.",
        "CoreIndicatorsForNationalSummary": "Core Indicators",
        "ConsitentTrend": "Consitent Trend (Yes/No)",
        "ConsitentOverTime": "Consistency over time for core indicators",
        "ConsitentOverTimeOptions": "Yes/No",
        "ConsitentOverTimeResponseError": "Please select 'Yes' or 'No' of 'Consistency Trend' for all core indicators",
        "NoRecordsAvailable": "No Records Available",
        "nationalLevelSummary": {
          "ReportCompleteness": "Completeness of reporting",
          "ReportTimeliness": "Timeliness of reporting",
          "VariableCompleteness": "Completeness of core variables within reports",
          "VariableConsistency": "Consistency between core variables",
          "VariableConcordance": "Concordance of core variables between two reporting systems",
          "ConsistencyOverTime": "Consistency over time for core indicators",
          "MalariaOutpatients": "Proportion of malaria outpatients",
          "MalariaInpatients": "Proportion of malaria inpatients",
          "MalariaInpatientsDeath": "Proportion of malaria inpatients death",
          "TestPositivityRate": "Test positivity rate",
          "SidePositivityRate": "Side positivity rate",
          "RDTPositivityRate": "RDT positivity rate",
          "suspectedTests": "Proportion of suspected tested",
          "dataQualityReason": "Reasons for observed data quality results",
          "dataQualityReasonText": "1.2.14 Reason for observed data quality results"
        },
        "Message": {
          "SaveServiceLevelSuccessMessage": "DQA for Service Level has been saved successfully.",
          "SaveServiceLevelErrorMessage": "Unable to save the DQA for Service Level.",
          "UpdateServiceLevelSuccessMessage": "DQA for Service Level has been updated successfully.",
          "UpdateServiceLevelErrorMessage": "Unable to update the DQA for Service Level.",
          "ServiceLevelUploadTemplateSuccessMessage": "DQA template for Service Level has been uploaded successfully.",
          "ServiceLevelUploadTemplateErrorMessage": "Unable to upload DQA template for Service Level.",
          "ServiceLevelUploadTemplateTryErrorMessage": "Sorry! Seems like something went wrong while uploading the file. Please try again...",
          "FinalizeServiceLevelSuccessMessage": "DQA for Service Level has been finalize successfully.",
          "FinalizeServiceLevelErrorMessage": "Unable to finalize the DQA for Service Level.",
          "DeleteServiceLevelSuccessMessage": "Service Level DQA tab has been deleted successfully.",
          "DeleteServiceLevelErrorMessage": "You are not authorized to delete this tab.",
          "DeleteServiceLevelDialogMessage": "Are you sure you want to delete this? Please confirm",
          "HmisLessCasesMessage": "HMIS has less cases than the source",
          "HmisMoreCasesMessage": "HMIS has more cases than the source",
          "HmisEqualCasesMessage": "HMIS=Source",
          "UpdateObservedDataQualitySuccessMessage": "Data quality reason for the DQA Service Level updated successfully.",
          "UpdateObservedDataQualityErrorMessage": "Unable to update Data quality reason for the DQA Service Level.",
          "FinalizeObservedDataQualitySuccessMessage": "Data quality reason for the DQA Service Level finalized successfully.",
          "FinalizeObservedDataQualityErrorMessage": "Unable to finalize Data quality reason for the DQA Service Level.",
          "SaveDeskLevelSuccessMessage": "DQA for Desk Level has been saved successfully.",
          "SaveDeskLevelErrorMessage": "Unable to save the DQA for Desk Level.",
          "UpdateDeskLevelSuccessMessage": "DQA for Desk Level has been updated successfully.",
          "UpdateDeskLevelErrorMessage": "Unable to update the DQA for Desk Level.",
          "FinalizeDeskLevelSuccessMessage": "DQA for Desk Level has been finalized successfully.",
          "FinalizeDeskLevelErrorMessage": "Unable to finalize the DQA for Desk Level.",
          "DeskLevelUploadTemplateSuccessMessage": "DQA template for Desk Level has been uploaded successfully.",
          "DeskLevelUploadTemplateErrorMessage": "Unable to upload DQA template for Desk Level.",
          "SaveEliminationDQASuccessMessage": "Elimination DQA has been saved successfully.",
          "SaveEliminationDQAErrorMessage": "Unable to save the Elimination DQA.",
          "FinalizeEliminationDQASuccessMessage": "Elimination DQA has been finalized successfully.",
          "FinalizeEliminationDQAErrorMessage": "Unable to finalize the Elimination DQA."
        }
      },
      "QuestionBankSurvey": {
        "FinalizeQuestionnaire": "Finalize Questionnaire",
        "ReFinalizeQuestionnaire": "Re-finalize Questionnaire",
        "GenerateQuestionnaire": "Generate Questionnaire",
        "UploadData": "Upload Data",
        "RespondentType": "Select respondent type",
        "SubnationalType": "subnational level",
        "SubnationalLevel": "subnational level surveillance office/unit",
        "ServiceDeliveryLevel": "service delivery",
        "CommunityLevel": "community",
        "SelfAssesment": "Do you want to include self assessment questionnaires ?",
        "HealthFacility": "Do you want to view list of health facilities?",
        "HealthFacilityTooltip": "These are the health facilities where the survey will be carried out. Download these to review. If you would like to change the health facilities prior to generating the questionnaire then please go back to health facility management in setup and upload a new file.",
        "RespondentTypeHeading": "Answer the following questions to configure the survey questionnaires",
        "ConfigureSurvey": "Configure survey questionnaires",
        "SurveySetup": "Survey Setup",
        "ConfigureSurveyQuestionnaire": "CONFIGURE SURVEY QUESTIONNAIRES",
        "HowToConfigure": "How to configure",
        "HowToConfigureTooltip": "Please review the questions that you would like to include in the final questionnaire by clicking on each objective, sub-objective and indicator. De-select questions that should not be included by unchecking the tick box. Mandatory questions cannot be deselected.<br/> The question text can, and should, be modified to suit the country context without changing the meaning of the question. Response options cannot be modified unless the responses are based on the results from the desk review.",
        "RespondentTypeTooltip": "This is the setting in which you would like to interview staff using a questionnaire.",
        "SelfAssesmentTooltip": "These questionnaires should be completed anonymously by the interviewee. The questions allow the interviewee to evaluate their own actions and attitudes towards performing surveillance tasks",
        "HowToConfigureSurveyQuestionnaire": "HOW TO CONFIGURE SURVEY QUESTIONNAIRES",
        "Question": "Question",
        "ResponseOptions": "Response options",
        "InterviewerNotes": "Notes for interviewer",
        "QuestionsFinalizeDialogContent": "Please 'Save' changes first before finalizing. Kindly note that your changes will be lost if you do not 'Save' them. Do you still want to 'Finalize' survey for",
        "BeforeSaving": "before saving",
        "QuestionsReFinalizeDialogContent": "Do you want to finalize survey questions for",
        "ConfigureSurveyDialogContent": "You have changed previously selected parameters which will modify existing questionnaires. Do you want to continue?",
        "ResponsePlaceholderText": "Response should be added from desk review",
        "Placeholder4BF0B217-9124-47DF-9385-2EC1DD63D652": "Please fill out information systems as per below order:\na. Primary information System [From Desk Review]\nb. Secondary information System [From Desk Review]\nc. Information System 3 [From Desk Review]\nd. Information System 4 [From Desk Review]",
        "PlaceholderC86F918D-ADC6-4DAA-99F5-4AB253A34510": "Please fill out the reporting tools as per order in Desk Review:\nForm/tool 1 [populate from desk review]\nForm/tool 2 [populate from desk review]\nForm/tool 3 [populate from desk review]\nForm/tool 4 [populate from desk review]\nForm/tool 5 [populate from desk review]\nForm/tool 6 [populate from desk review]\nForm/tool 7 [populate from desk review]\nForm/tool 8 [populate from desk review]\nForm/tool 9 [populate from desk review]\nForm/tool 10 [populate from desk review]",
        "PlaceholderE4769D5D-ED62-4BA2-8903-9828D682BB7C": "Please fill out the outputs as per order in Desk Review:\nOption 1\nOption 2\nOption 3\nOption 4\nOption 5\nOption 6",
        "Placeholder61C11183-7F06-497E-ADC5-9080B6BC91AA": "a. {add choices from the Desk Review}",
        "UploadShellTable": "Upload shell table",
        "FinalizeRespondentType": "Please finalze all the respondent type questions to upload shell table",
        "PublishAssessment": "Assessment is already published",
        "ExportRawSurveyDataByHFC": "Export raw survey data by health facility.",
        "ExportRawSurveyDataByDistrict": "Export raw survey data by District.",
        "Message": {
          "SaveRespondentTypesSuccessMessage": "Respondent types saved successfully.",
          "SaveSurveyQuestionSuccessMessage": "Survey questions saved successfully.",
          "FinalizeSurveyQuestionSuccessMessage": "Survey questions has been finalize successfully.",
          "FinalizeSurveyQuestionErrorMessage": "Unable to finalize the survey for questions.",
          "HealthFacilityUploadTemplateSuccessMessage": "Health facilities for shell table has been uploaded successfully.",
          "HealthFacilityUploadTemplateErrorMessage": "Unable to upload health facilities template for shell table.",
          "GenerateQuestionnaireSuccessMessage": "Questionnaire generated successfully.",
          "GenerateQuestionnaireErrorMessage": "Unable to generate the questionnaire.",
          "HealtFacilityNoDataMessage": "Before the questionnaire can be configured the health facilities where the survey will be carried out must be uploaded under the health facility management.",
          "ErrorMsgRequired": "Please enter question response for indicators"
        }
      },
      "ShellTable": {
        "Message": {
          "ShellTableUploadTemplateSuccessMessage": "Shell table has been uploaded successfully.",
          "ShellTableUploadTemplateErrorMessage": "Unable to upload shell table."
        }
      },
      "IndicatorDataSaveSuccessMessage": "{{sequence}} response captured successfully."
    },
    "DataAnalysis": {
      "ViewDiagram": "View Diagram",
      "CompleteAssessmentForDataAnalysis": "Please complete assessment of indicators in Desk review to view analysis results here",
      "QuestionBankForDataAnalysis": "Please upload shell table data in survey results under data collection to view analysis results here",
      "RespondentType": "Respondent Type",
      "HealthFacilityType": "Health Facility Type",
      "SaveDataAnalysisNationalSummaryResult": "Summary Result saved successfully.",
      "FinalizedDataAnalysisNationalSummaryResult": "Summary Result finalized successfully.",
      "DQASummaryTooltip": "Decide whether the trends of each core indicator are consistent over time, by indicating \"Yes or No\" below. If all trends are consistent and can be explained and are not due to data quality issues then this indicator is met. If any of the trends are inconsistent due to data quality issues then this indicator is not met.",
      "DQASummaryInfoText": "*If an indicator was not assessed, please select 'yes' for consistency over time so that it does not impact negatively on the final result (met/not met)."
    },
    "ReportGeneration": {
      "ScoreCard": {
        "FinalizeScoreCard": "Finalize Score Card",
        "ReFinalizeScoreCard": "Re-finalize Score Card",
        "GenerateScoreCard": "Generate Score Card",
        "Sequence": "Sequence",
        "Indicator": "Indicator",
        "NationalScore": "National Score",
        "NationalResult": "National Result",
        "ObjectiveNationalScore": "Objective Result",
        "SubObjectiveNationalScore": " Sub-Objective Result",
        "DeskReviewDQAScore": "Result",
        "ServiceDeliveryScore": "Service Delivery Score",
        "ServiceDeliveryResult": "Service Delivery Result",
        "RapidDeskLevelAndServiceDeliveryScore": "Result",
        "NonRapidDQAScore": "Desk Review and DQA Result",
        "SurveyScore": "Survey Score",
        "SurveyResult": "Survey Result",
        "IndicatorsMet": "Indicators Met",
        "ReasonForResult": "Reason For Result",
        "Recommendation": "Recommendation",
        "SurveyScoreFinalizeDialogContent": "Please 'Save' changes first before finalizing. Kindly note that your changes will be lost if you do not 'Save' them. Do you still want to 'Finalize' survey for",
        "BeforeSaving": "before saving",
        "ScoreCardFinalizeDialogContent": "Do you want to finalize score card?",
        "ConfigureSurveyDialogContent": "You have changed previously selected parameters which will modify existing score card survey. Do you want to continue?",
        "PrerequisiteToViewScoreCard": "All priority indicators for desk review, DQA and shell table upload should be completed.",
        "PrerequisiteToViewScoreCard_Rapid": "All priority indicators for desk review and DQA upload should be completed.",
        "ComprehensiveScoreCard": "The scorecard is automatically populated from the desk review and the DQA. The final results of the survey should be selected manually. Guidance on how to determine whether indicators are met, partially met or not met for indicators assessed in the survey can be found in the Implementation Reference Guide. Final results are based on the following calculation; the sum of points for each indicator (Met=2, Partially met=1 and Not met=0)/maximum total points (number of indicators assessed x 2). Indicators that were not assessed are removed from the calculation. Please add more details on the reason for the result (e.g achievements and challenges) and provide key recommendations for surveillance system strengthening.",
        "RapidScoreCard": "The scorecard is automatically populated from the desk review and the DQA. Final results are based on the following calculation  <i> the sum of points for each indicator (Met=2, Partially met=1 and Not met=0)/maximum total points (number of indicators assessed x 2).</i> Indicators that were not assessed are removed from the calculation. Please add more detail on the reason for the result (e.g achievements and challenges) and provide key recommendations for surveillance system strengthening.",
        "EliminationActivities": "Elimination Activities",
        "NoteForResultAndRecommendation": "If text is entered for 'reason for result' and/or 'recommendation', please make sure to click save before moving between sub-objectives.",
        "Message": {
          "SaveScroreCardSuccessMessage": "Saved successfully.",
          "SaveScroreCardErrorMessage": "Unable to save the survey for score card.",
          "SaveScoreCardNothingChangedMessage": "There are no changes detected for 'Save'.Please update the record before saving.",
          "FinalizeScroreSuccessMessage": "Scorecard has been successfully finalised.",
          "FinalizeScroreErrorMessage": "Unable to finalize the survey for score card.",
          "ScroreCardGenerationSuccessMessage": "Generate the survey for score card.",
          "ScroreCardGenerationErrorMessage": "Unable to generate the survey for score card.",
          "ScoreCardSaveFinalizeErrorMessage": "Unable to finalize the survey for score card.Please 'Save' changes first before finalizing."
        }
      }
    },
    "Message": {
      "CreateSuccessMessage": "Assessment created successfully.",
      "UpdatedSuccessMessage": "Assessment updated successfully.",
      "FinalizedSuccessMessage": "Assessment finalized successfully.",
      "FinalizeErrorMessage": "Sorry! Couldn't finalize Assessment.",
      "SaveIndicatorSuccessMessage": "Indicators are successfully saved for an Assessment.",
      "SaveIndicatorsErrorMessage": "Unable to save the indicators.",
      "UploadDiagramSuccessMessage": "The diagram is uploaded successfully.",
      "UploadObjectiveDiagramSuccessMessage": "Diagram and other details are saved successfully.",
      "IndicatorReportExportSuccessMessage": "Indicators response are successfully exported.",
      "IndicatorReportExportErrorMessage": "Unable to export the indicator response."
    },
    "HealthFacilities": {
      "HealthFacilitiesHeading": "Health Facilities",
      "HealthFacilitiesDesc": "This feature allows you to upload the health facilities where the survey will be carried out.<br/> Begin by generating the template. Populate the excel template with the health facility data and upload it back into the system. <br/>This data will be used to automatically populate a list of health facilities in the questionnaire and the shell table workbooks generated for the survey.",
      "ViewHealthFacility": "DOWNLOAD HF",
      "GenerateTemplate": "Generate Template",
      "FileUploadSize": "Max. Size Limit (25 mb)",
      "HealthFacilityFileName": "Health Facility Template"
    }
  },
  "Landing": {
    "AssessmentsSummary": "Surveillance Assessments",
    "SurveillanceAssessments": "Surveillance system assessments",
    "SurveillanceAssessmentDesc": "emphasizes surveillance as a core intervention for accelerating progress towards malaria elimination across endemic settings. Robust surveillance systems are needed to accurately and reliably track the burden of malaria,monitor the implementation of interventions aimed at reducing cases and deaths, and assess their impact.",
    "SurveillanceAssessmentDescOne": "recommends regular malaria surveillance assessments which systematically measure how well malaria surveillance systems are performing. The results of malaria surveillance assessments can be used to provide actionable and prioritized recommendations on how to strengthen surveillance systems, which can be used to develop national and subnational strategic and operational plans. In elimination settings a surveillance assessment can help the country to prepare documentation and check the quality of data prior to certification.",
    "SurveillanceAssessmentsLink": "The WHO Global Technical Strategy for Malaria 2016–2030",
    "SurveillanceAssessmentsLinkOne": "Malaria Surveillance, Monitoring & Evaluation",
    "AssessmentsDesc": "recommends regular malaria surveillance assessments which systematically measure how well malaria surveillance systems are performing. The results of malaria surveillance assessments can be used to provide actionable and prioritized recommendations on how to strengthen surveillance systems, which can be used to develop national and subnational strategic and operational plans. In elimination settings a surveillance assessment can help the country to prepare documentation and check the quality of data prior to certification.",
    "Tools": "Tools",
    "ToKnowMore": "To Know More",
    "NonWHOUserRegister": "Registration for Non WHO users",
    "Assessment": "Assessment framework",
    "DeskReview": "Desk Review",
    "QuestionBank": "Question Bank",
    "DataQualityAnalysis": "Data Quality Analysis",
    "AnalysisTool": "Analysis tools",
    "ReportTemplates": "Report template",
    "ConceptNoteProtocol": "Concept note and protocol",
    "DownloadInprogress": "Download in progress",
    "AssessmentFramework": "A set of objectives, sub-objectives, and indicators that can be used to quantify and/or qualify strengths and weaknesses in the surveillance system. This tool should be used as the starting point in an assessment to define the scope of the assessment (selection of the transmission setting, malaria control interventions and strategies and indicators) and the approach ",
    "AssessmentApproach": "(rapid, tailored or comprehensive).",
    "AssessmentDeskReview": "A set of questions, tables, graphics and diagrams used to collect information and summarise what is known about malaria surveillance. Information is collected through document and data review at the national level, and through interviews or more informal discussions with surveillance programme staff and other relevant supporting partners.",
    "AssessmentDataQualityGraphic": "Tools and guidance for collecting and analysing data to specifically assess data quality (completeness, timeliness, consistency and concordance) at national, regional, district and service delivery levels. At the desk level data are extracted from national databases and used to populate a template which automatically generates tables and graphics. At the service delivery level data extracted from the national database is compared with data collected at the health facility.",
    "AssessmentQuestionBank": "A library of questions which can be used to develop survey questionnaires for data collection at sub-national (region/district), service delivery or community levels.",
    "AssessmentAnalysisTool": "A set of shell tables in excel used to summarise the results from the survey.",
    "AssessmentReportTemplates": "A report template for organizing, visualizing, and interpreting results from the assessment. A technical brief is used to highlight a subset of priority results, whereas the complete report includes all assessment results.",
    "AssessmentProtocol": "A template for the outline of a short concept note for refining the scope, methods, expected outputs and outcomes of an assessment and a more detailed protocol outline required for comprehensive assessments.",
    "SurveillanceAssessmentToolkit": "The malaria surveillance assessment toolkit",
    "SurveillanceAssessmentToolkitDesc": "The malaria surveillance assessment toolkit provides a standardised but adaptable assessment framework and an associated package of tools which allows results to be compared between countries, between regions within a country, or over time. The assessment framework is based on",
    "SurveillanceAssessmentDescription": "Defined by the WHO,",
    "FourKeyObjectives": "four key objectives :",
    "KeyObjectives": "Key Objectives",
    "SurveillanceAssessmentToolkitDesc1": "performance, context and infrastructure, technical and processes and behaviour. A set of associated sub-objectives and indicators are used to evaluate performance and the determinants of that performance. The toolkit consists of two modules specifically tailored to assess surveillance in burden reduction and/or elimination settings. A high-level surveillance assessment of other malaria control interventions and",
    "SurveillanceAssessmentToolkitDesc2": "strategies can also be carried out to understand how data is collected and used alongside routine malaria case surveillance data. Further details on the toolkit and a step-by-step guide on how to carry out a malaria surveillance assessment are shown in the",
    "ImplementationReferenceGuide": "Implementation Reference Guide.",
    "AssessmentApproaches": "Assessment Approaches",
    "LandingTable1Heading": "Overview of the assessment framework",
    "LandingTable2Heading": "Assessment approaches using the toolkit",
    "ReadMore": "Read More",
    "ReadLess": "Read Less",
    "An": "An",
    "Overview": "Overview",
    "NonWHOUsers": "For Non WHO users",
    "SurveillanceAssessmentToolkitOverviewDesc": "of the toolkit is also provided",
    "Carousel": {
      "TotalCreatedAssesments": "Surveillance assessments initiated",
      "TotalInitiatedAssesments": "Surveillance assessments in progress",
      "TotalNumberOfCountriesWithOneAssesment": "Completed 1 surveillance assessment",
      "TotalNumberOfCountriesWithMoreThanOneAssesment": "Completed more than 1 surveillance assessments",
      "TotalCompletedRapidAssessments": "Rapid surveillance assessments",
      "TotalCompletedTailoredAssesments": "Tailored surveillance assessments",
      "TotalCompletedComprehensiveAssesments": "Comprehensive surveillance assessments",
      "TotalYearWiseAssessments": "Surveillance assessments in progress or completed",
      "SelectAssessmentsYear": "Select surveillance assessments year",
      "CompletedAssesmentCountries": "Surveillance assessment completed"
    },
    "LandingTable2": {
      "Approach": "Approach",
      "Rapid": "Rapid",
      "Tailored": "Tailored",
      "Comprehensive": "Comprehensive",
      "Scope": "Scope",
      "ScopeRapidLabel": "Only priority indicators for surveillance of malaria cases and deaths by transmission setting(burden reduction and/or elimination) and priority indicators for other malaria control and intervention strategies implemented in the country selected for assessment",
      "ScopeTailoredLabel": "Priority indicators + user-selected optional indicators of interest for surveillance of malaria cases and deaths by transmission setting, and priority indicators for other malaria control and intervention strategies implemented in the country selected for assessment.",
      "ScopeComprehensiveLabel": "All indicators for surveillance of malaria cases and deaths by transmission setting, and priority indicators for all other malaria control and intervention strategies implemented in the country.",
      "Methods": "Methods",
      "MethodsRapidLabel": "Primarily limited to desk review with a few essential site visits",
      "MethodsTailoredLabel": "Desk review and surveys at different levels of the health systems (i.e., national, subnational, a sample of facilities and community healthcare workers)",
      "MethodsComprehensiveLabel": "Desk review and surveys at different levels of the health systems (i.e., national, subnational, a sample of facilities and community healthcare workers)",
      "EstimatedResourceRequirement": "Estimated resource requirement",
      "ResourceRequirementRapidLabel": "Low; 2-4 weeks ",
      "ResourceRequirementTailoredLabel": "Medium/high: at least 3 months and up to 12 months depending on context",
      "ResourceRequirementComprehensiveLabel": "High: at least 3 months and up to 12 months depending on context",
      "SuggestedFrequency": "Suggested frequency",
      "SuggestedFrequencyRapidLabel": "Once every 3-5 years in line with the MPR and NSP development. Annual in elimination settings or if desired in burden reduction settings to monitor progress towards improvements.",
      "SuggestedFrequencyTailoredLabel": "Once every 3-5 years in line with the MPR and NSP development. Annual in elimination settings depending on need and resources.",
      "SuggestedFrequencyComprehensiveLabel": "Once every 3-5 years in line with the MPR and NSP development. Annual in elimination settings depending on need and resources."
    },
    "LandingTable1_1": {
      "Objective": "Objective",
      "Name": "Name",
      "Description": "Description",
      "SubObjective": "Sub-objective",
      "Performance": "Performance",
      "MeasureThePerformance": "Measure the performance of the surveillance system",
      "SurveillanceSystemCoverage": "Surveillance system coverage",
      "SurveillanceSystemCoverageLabel": "Assess whether malaria cases and deaths are accurately captured by surveillance at each level of the health system",
      "DataQuality": "Data quality",
      "DataQualityLabel": "Measure the quality of data collected at the service delivery level, and reported to subnational and national levels (completeness, timeliness, concordance, and consistency)",
      "DataUse": "Data use",
      "DataUseLabel": "Identify evidence of data-informed programme planning and use of data for decision making"
    },
    "LandingTable1_2": {
      "ContextInfrastructure": "Context and Infrastructure ",
      "EvaluateInfrastructure": "Describe and evaluate contextual and infrastructural aspects of the surveillance that may influence performance. This includes an assessment of health sectors reporting, whether minimum data are captured for malaria control and interventions and strategies implemented in the country, information systems used, availability of and adherence to guidelines, human and financial resources, and infrastructure.",
      "SurveillanceSectors": "Surveillance sectors and strategies",
      "DescribeSurveillanceSectors": "Describe surveillance for malaria control strategies and sectors reporting core indicators at each level of the health system and evaluate definitions and algorithms used",
      "InformationSystems": "Information systems",
      "DescribeInformationSystems": "Describe information systems used for malaria surveillance and evaluate their flexibility, acceptability, functionality, and interoperability/ integration ",
      "Guidelines": "Guidelines and standard operating procedures (SOPs)",
      "DescribeGuidelines": "Evaluate the availability and content of key documents (guidelines, procedure manuals, and regulations) for malaria surveillance",
      "Resources": "Resources",
      "DescribeResources": "Identify the staff, equipment, and infrastructure required for malaria surveillance and evaluate what is available at all levels of the health system",
      "FinancialSupport": "Financial support",
      "DescribeFinancialSupport": "Describe the budget available for malaria surveillance and identify any gaps"
    },
    "LandingTable1_3": {
      "TechnicalProcesses": "Technical and Processes",
      "DescribeTechnicalProcesses": "Describe and evaluate processes and technical aspects of the surveillance system that may influence performance. This includes an assessment of processes, tools and personnel involved with the flow and use of data from recording to response.",
      "CaseManagement": "Case management",
      "DescribeCaseManagement": "Evaluate case management including standardized use of case definitions and adequate commodities for testing and treatment",
      "Recording": "Recording",
      "DescribeRecording": "Describe and evaluate the data recording processes (e.g., tools, personnel, and frequency from each point-of-care type)",
      "Reporting": "Reporting",
      "DescribeReporting": "Describe and evaluate the flow of information through the surveillance system (e.g., tools, personnel, and frequency at each level of the health system)",
      "Analysis": "Analysis",
      "DescribeAnalysis": "Describe and evaluate the analysis process and expected outputs",
      "QualityAssurance": "Quality assurance",
      "DescribeQualityAssurance": "Describe and evaluate the activities, feedback processes, and mechanisms in place to ensure data quality (i.e., data cleaning, supervision, data quality assessments, and data review meetings, checking for duplicates and internal consistency)",
      "DataAccess": "Data access",
      "DescribeDataAccess": "Describe and evaluate access to data in the surveillance system (accessing database or requesting, personnel, frequency)"
    },
    "LandingTable1_4": {
      "Behavior": "Behaviour",
      "DescribeBehavior": "Describe and evaluate behavioural aspects of the surveillance system that may influence performance. This includes an assessment of governance structures in place and the promotion of an information culture, as well as proficiency, motivation and accountability of staff involved in malaria surveillance within a country.",
      "Governance": "Governance",
      "DetermineGovernance": "Determine the governance structures in place for malaria surveillance, including documented planning, targets, and organizational structure as well as external oversight",
      "Promotion": "Promotion of an information culture",
      "DeterminePromotion": "Determine the processes in place to promote a culture of data use and resulting perceptions among surveillance staff (i.e., are staff encouraged to use data and are they motivated to produce quality data)",
      "Supervision": "Supervision",
      "DetermineSupervision": "Describe and evaluate the processes in place for supervision and management of surveillance staff",
      "SurveillanceStaff": "Surveillance staff proficiency",
      "DetermineSurveillanceStaff": "Determine the processes in place and resulting perceptions of job competence among surveillance staff (i.e., are staff competent at designated surveillance tasks and how (e.g., training/job aids))"
    }
  },
  "Exception": {
    "InSufficientPrivilegesToAddExistingWHOUser": "You do not have sufficient privileges to add a user since only WHO users can add existing WHO users OR if you are a WHO user then please try re-logging into the application and adding a user again.",
    "InSufficientPrivilegesToAddUser": "You do not have sufficient privileges to add a user.",
    "InSufficientPrivilegesToSendInvite": "You do not have sufficient privileges to send an invite.",
    "SameDomainAsAzureADCannotBeInvited": "This user cannot be invited because the user's email address is of an internal WHO member. Please use the \"Assign existing user\" tab",
    "InvalidValue": "Invalid value provided for the field",
    "FieldShouldNotBeEmpty": "Field should not be empty.",
    "IdentityWithEmailAlreadyExist": "User with {0} email address already exists in the application. Please add a user again with a different email address.",
    "EmailIsNotValid": "Email is not valid.",
    "OnlySuperManagerAccess": "Only super managers have access to the requested resource.",
    "OnlyViewerAccess": "Only viewers have access to the requested resource.",
    "SuperManagerAlreadyPresent": "Country already has a Super Manager.",
    "AccessToOnlyWHOADUser": "The email provided does not exist in the list of registered WHO users (WHO active directory). Please make sure the user has been registered and try again.",
    "AccessToOnlyWHOUser": "Only WHO users have access to the requested resource.",
    "UserStatusMustBePending": "User status must be pending to send activation.",
    "InvalidUserType": "User type is invalid.",
    "UserMustBeSuperManagerOfCountry": "User must be a super manager of country",
    "PasswordShouldBeStrong": "Entered password is not strong.",
    "UserNameAlreadyExists": " Username already exists.",
    "CustomerEmailAlreadyExist": "Customer with this email already exists.",
    "PermissionDeniedToFinalize": "You don't have permission to finalize the assessment.",
    "UserCantRequestOtherCountry": "User cannot request for other country access.",
    "InvalidIndicatorsSelectedForStrategy": "Invalid indicators selected for given strategies.",
    "NoEditPermissionOnAssessment": "User does not have edit permission on assessment.",
    "AssessmentDoesNotExist": "Assessment does not exist.",
    "InvalidDateRange": "Date range is invalid.",
    "UpdateAssessmentDateIsInvalid": "Dates to be updated in assessment are not valid.",
    "UserAlreadyRequestedForCountry": "User already requested for access to given countries.",
    "AssessmentAlreadyCreatedForTheCountryForYear": "Assessment for this year is already created.",
    "InvalidUserTypeUpdateBySuperManager": "Super manager can only update user type to Viewer or Manager.",
    "RestrictToDeactivateManager": "Manager with active assessments can not be deactivated.",
    "AtleastOneOptionalIndicatorBeSelected": "Please select at least one optional indicator.",
    "InvalidMonth": "Please select valid month",
    "InvalidYear": "Please select valid year",
    "InvalidFile": "Please select valid file",
    "InvalidDiagramFile": "Please select valid file. Must be from (.png, .jpg, .jpeg)",
    "InvalidMonthRange": "Month range is invalid",
    "NoPermissionToCreateTemplate": "User doesn't have permission to create template.",
    "NoPermissionToUpload": "User doesn't have permission to upload template.",
    "NoPermissionToUploadDiagram": "User doesn't have permission to upload the diagram.",
    "NoPermissionToUpdateObservedDataQualityReason": "User doesn't have permission to update the reasons for the observed data quality results",
    "SelectFile": "Please select file",
    "InvalidRecentYear": "Please upload valid most recent year template",
    "InvalidRegisterTypeSelected": "Please provide valid register type",
    "UserDoesNotHavePermission": "User does not have permission to perform this operation",
    "ServiceLevelIsFinalized": "Finalized service level can not be deleted",
    "ServiceLevelDoesNotExist": "Service level record does not exist",
    "DuplicateDataSourceSelected": "You can not select HMIS/DHIS 2 data source from both data system sources",
    "DuplicateTextForOtherDataSystemSource": "Please enter different text for Other data system sources for both data systems.",
    "InvalidStartPeriodDate": "Please select valid start period date",
    "OnlyServiceLevelRecordPresent": "Can not delete last service level record",
    "CanNotProcessTemplate": "Sorry! we cannot process your request. Please upload a valid excel file pertaining to the current Tab setting.",
    "InvalidFileSize": "File size should not be greater than 25MB",
    "FileSizeGreaterThan10MB": "File size should not be greater than 10MB",
    "FileSizeAndTypeShouldBeValid": "File size should not be greater than 10 MB and its type must be from (.png, .jpg, .jpeg)",
    "AtleastOneDiagramIsRequired": "Please upload at least one diagram",
    "TemplateEmpty": "Data must be present for all fields in the Summary Result sheet. Please fill out the data and upload the template again",
    "InvalidVariableIds": "Please provide valid list of variable ids",
    "InvalidDRResponseStatus": "Please provide valid desk review response status",
    "ResponseMappingKeyIsNotFound": "Something went wrong. Please contact to administrator",
    "InvalidYearMonthRange": "Please select month where 'To month' is not equal to 'From month' and within the range of 12 months from 'From month'",
    "ContainExecutables": "The file contained an executable (application code), which can be a significant risk factor. Hence, It is not allowed.",
    "ContainsScript": "The file contained a script that can be a significant risk factor. Hence, It is not allowed.",
    "ContainsPasswordProtectedFile": "The file contained a password protected or encrypted file which is not allowed.",
    "ContainsRestrictedFileFormat": "The file type of the uploaded file is restricted. Please upload a valid file.",
    "ContainsMacros": "The uploaded file contains embedded Macros of other embedded threats within the document, which can be a significant risk factor. Hence, It is not allowed.",
    "ContainsXmlExternalEntities": "The uploaded file contains embedded XML External Entity threats of other embedded threats within the document, which can be a significant risk factor. Hence, It is not allowed.",
    "ContainsInsecureDeserialization": "The uploaded file contains invalid content.",
    "ContainsHtml": "The uploaded file contains HTML tags that are not allowed.",
    "VirusScanFailed": "Something went wrong while uploading the file. Please contact to administrator.",
    "InvalidRespondentTypeSelected": "Please provide valid respondent type",
    "UserHasActiveAssessmentAsManager": "User having active assessments as a manager can not be deactivated.",
    "AtLeastOneVariableShouldBeSelectedForConcordance": "Please select at least one variable for concordance.",
    "AtLeastOnePriorityVariableShouldBeSelected": "At-least one priority variable should be selected.",
    "DataSourceCannotBeSame": "Data source of data system 1 and data source of data system 2 can not be the same.",
    "MultipleDistrictsHaveSameDistrictCode": "The uploaded file contains same district code for multiple districts.",
    "MultipleHealthFacilitiesHaveSameCode": "The uploaded file contains same code for multiple health facilities.",
    "MultipleRecordsPresentInHealthFacilitySheet": "Health facility sheet in uploaded shell table file contains more than one records.",
    "SameValuesPresentForMultipleRowsInHealthFacilitySheet": "The uploaded file contains same values for multiple rows in health facilities.",
    "NoRecordsPresentInHealthFacilitySheet": "No record present in health facility sheet in uploaded shell table file.",
    "SuperManagerCountrySameAsHealthFacility": "The uploaded health facility sheet contains invalid country.",
    "InvalidHealthFacilityType": "The uploaded file contains invalid health facility type.",
    "InvalidHealthFacilitySheet": "The uploaded file contains invalid health facility sheet.",
    "InvalidHealthFacilitySheetData": "All fields are mandatory to upload health facilities. Unable to upload health facilities template for shell table as mandatory fields are missing.",
    "MultipleCountriesNotAllowedInHealthFacilitySheet": "The uploaded health facility sheet contains multiple countries.",
    "NoPermissionToGenerateQuestionBankTemplate": "User doesn't have permission to generate question bank template.",
    "NoPermissionToExportShellTableTemplate": "User doesn't have permission to export shell table data.",
    "MalariaToolKitFileName": "Please upload a file with the name of MalariaSurveillanceToolkitTools.zip.",
    "NoHealthFacilityData": "Please upload health facilities through Health Facility Management first and then you will be able to upload a shell table.",
    "InvalidZipFileSize": "File size should not be greater than 50MB",
    "WHO_INT_UsersNotAllowedUseToRegister": "User registration is not allowed with who.int email address; instead, you can proceed with the login option or contact the system administrator.",
    "InvalidHealthFacility": "The uploaded file contains invalid health facility.",
    "AccessTokenExpiredWHOADUser": "The email provided does not exist in the list of registered WHO users (WHO active directory) or your token has expired. Please relogin and try again.",
    "InSufficientPrivilegesToSendEmail": "You do not have sufficient privileges to send an email."
  },
  "Month": {
    "January": "January",
    "February": "February",
    "March": "March",
    "April": "April",
    "May": "May",
    "June": "June",
    "July": "July",
    "August": "August",
    "September": "September",
    "October": "October",
    "November": "November",
    "December": "December"
  },
  "Report": {
    "Publish": "Publish",
    "OptOut": "Do you want to opt out from publishing the summary results of this assessment on the global dashboard?",
    "PublishDialogContent": "User will not be able to modify the assessment and it's content once 'Published'. Are you sure you want to publish the assessment?",
    "OptOutDialogContent": "Do you want to opt out assessment from global dashboard?",
    "OptInDialogContent": "Do you want to opt in assessment on global dashboard?",
    "OptOutInfoText": "If user opts out then results for that assessment will not be displayed on the global dashboard.",
    "FileName": "File Name",
    "FileSize": "File Size",
    "Download": "Download",
    "Delete": "Remove",
    "Message": {
      "PublishSuccessMessage": "Assessment has been publish successfully.",
      "PublishErrorMessage": "Unable to publish assessment.",
      "OptOutSuccessMessage": "Assessment opt out successfully.",
      "OptInSuccessMessage": "Assessment opt in successfully.",
      "OptOutErrorMessage": "Unable to opt out assessment.",
      "UploadFileSuccessMessage": "Report file has been uploaded successfully.",
      "UploadFileErrorMessage": "Unable to upload report file.",
      "NoFileUploaded": "There was no file uploaded",
      "DeleteFileSuccessMessage": "Report file has been deleted successfully.",
      "DeleteFileErrorMessage": "Unable to delete report file"
    }
  },
  "DataQualityAnalysis": {
    "TotalMalariaCases": "Total malaria  cases (confirmed + presumed)",
    "ConfirmMalariaCases": "Confirmed malaria cases",
    "MicroscopyTested": "Microscopy tested",
    "RDTTested": "RDT tested",
    "MicroscopyPositive": "Microscopy positive",
    "RDTPositive": "RDT positive",
    "AllCauseOutpatients": "All cause outpatients",
    "AllCauseInpatients": "All cause inpatients",
    "AllCauseDeaths": "All cause deaths",
    "MalariaInpatients": "Malaria inpatients",
    "MalariaInpatientDeaths": "Malaria inpatient deaths",
    "ConfirmedMalariaCasesTreated": "Confirmed malaria cases treated with 1st line treatment courses (incl ACT)",
    "SuspectedMalariaCases": "Suspected malaria cases",
    "PresumedMalariaCases": "Presumed malaria cases",
    "IPTp": "IPTp 1-4",
    "ANC": "ANC1-4",
    "RDTTested_RDTPositive": "RDT tested >/= Number of RDT positive",
    "MicroscopyTested_MicroscopyPositive": "Microscopy tested >/= Microscopy positive",
    "AllCauseOutpatients_TotalMalariaCases": "All cause outpatients > Total malaria cases (presumed + confirmed)",
    "AllCauseInpatients_MalariaInpatients": "All cause inpatients > Malaria inpatients",
    "AllCauseDeaths_MalariaInpatientDeaths": "All cause deaths > Malaria inpatient deaths",
    "ConfirmMalariaCases_ConfirmedMalariaCasesTreated": "Confirmed malaria cases >/= Confirmed malaria cases treated with 1st line treatment courses (incl ACT)",
    "Suspectedcases_RDTTested_RDTPositive": "Suspected cases >/= Microscopy positive +RDT positive",
    "ProportionOfMalariaOutpatients": "Proportion of malaria outpatients",
    "ProportionOfMalariaInpatients": "Proportion of malaria inpatients",
    "ProportionOfMalariaInpatientDeaths": "Proportion of malaria inpatient deaths",
    "TestPositivityRate": "Test positivity rate",
    "SlidePositivityRate": "Slide positivity rate",
    "RDTPositivityRate": "RDT positivity rate",
    "ProportionOfSuspectsTested": "Proportion of suspects tested",
    "Province": "Province",
    "District": "District",
    "HealthFacilityName": "Health Facility",
    "HealthFacilityType": "Public / Private",
    "Year": "Year",
    "Month": "Month",
    "IsReportsOnTime": "Reports On Time",
    "IsReportsReceived": "Reports Received",
    "IsExpectedReports": "Expected Reports",
    "Percentage": "Percentage",
    "CompletenessReportNumeratorHeader": "Number of reports received in a specified time period with ALL core variables completed",
    "CompletenessReportDenomeratorHeader": "Number of reports received in the same specified time period",
    "CompletenessReportResultHeader": "Completeness of key variables within reports (%)",
    "ConsistencyReportNumeratorHeader": "Number of reports received in a specified time period where ALL consistency checks between core variables are passed",
    "ConsistencyReportDenomeratorHeader": "Number of reports received in the same specified time period",
    "ConsistencyReportResultHeader": "Consistency (%)",
    "ConcordanceReportNumeratorHeader": "Number of reports received in a specified time period from two different data sources where 100% of core variables match",
    "ConcordanceReportDenomeratorHeader": "Number of paired reports from the two data sources",
    "ConcordanceReportResultHeader": "Concordance (%)",
    "NationalLevelReport": "National Level Report",
    "ProvinceLevelReport": "Province Level Report ",
    "DistrictLevelReport": "District LevelReport",
    "HealthFacilityLevelReport": "Health Facility Report",
    "SummaryReportName": "Summary result",
    "SummaryReportTitle": "Summary table of the year",
    "CompletenessOfReporting": "Completeness of reporting",
    "TimelinessOfReporting": "Timeliness of reporting",
    "VariableCompleteness": "Completeness variables",
    "ConsistencyBetweenVariable": "Consistency between variable",
    "ConsistencyOverTime": "Consistency over time",
    "Concordance": "Concordance",
    "1_2_1_CompletenessOfreports": "1.2.1 Completeness of reports",
    "1_2_3_TimelinessOfreporting": "1.2.3 Timeliness of reporting",
    "1_2_7_CompletenessOfCoreVariablesWithinReports": "1.2.7 Completeness of core variables within reports",
    "1_2_8_ConsistencyBetweenCoreVariables": "1.2.8 Consistency between core variables",
    "1_2_9_ConsistencyOverTimeForCoreIndicators": "1.2.9 Consistency over time for core indicators",
    "1_2_10_ConcordanceOfKeyVariablesBetweenTwoReportingSystems": "1.2.10 Concordance of key variables between two reporting systems",
    "1_ProportionOfMalariaOutpatients": "1.Proportion of malaria outpatients",
    "2_ProportionOfMalariaInpatients": "2.Proportion of malaria inpatients",
    "3_ProportionOfMalariaInpatientDeaths": "3.Proportion of malaria inpatient deaths",
    "4_TestPositivityRate": "4.Test positivity rate",
    "5_SlidePositivityRate": "5.Slide positivity rate",
    "6_RDTPositivityRate": "6.RDT positivity rate",
    "7_ProportionOfSuspectsTested": "7.Proportion of suspects tested",
    "1_2_1_charts": "1.2.1 charts",
    "1_2_3_charts": "1.2.3 charts",
    "1_2_7_charts": "1.2.7 charts",
    "1_2_8_charts": "1.2.8 charts",
    "1_2_9_charts": "1.2.9 charts",
    "1_2_10_charts": "1.2.10 charts",
    "NationalLevel": "National Level",
    "ProvinceLevel": "Province Level",
    "DistrictLevel": "District Level",
    "HealthFacilityLevel": "Health Facility Level",
    "HealthFacilitySummary": "Health Facility - Summary",
    "NationalLevelSummary": "National Level - Summary",
    "ProvinceLevelSummary": "Province Level - Summary",
    "DistrictSummary": "District - Summary",
    "ProvinceSummary": "Province - Summary",
    "PriorityVariable": "Priority Variable",
    "OptionalVariable": "Optional Variable",
    "PriorityVariablePercentage": "Priority Variable, %",
    "OptionalVariablePercentage": "Optional Variable, %",
    "NationalLevelPercentage": "National Level, %",
    "ProvinceLevelPercentage": "Province Level, %",
    "DistrictLevelPercentage": "District Level, %",
    "ANC1-4": "ANC1-4"
  },
  "QuestionBank": {
    "SubnationalLevel": "Subnational Level",
    "ServiceDeliveryLevel": "Service Delivery Level",
    "CommunityLevel": "Community Level",
    "CountryName": "Country name",
    "Region": "Region",
    "DistrictCode": "District code",
    "DistrictName": "District name",
    "HealthFacilityName": "Health facility name",
    "HealthFacilityCode": "Health facility code",
    "HealthFacilityType": "Health facility type",
    "RespondentType": "Respondent type"
  },
  "DataAnalysisExport": {
    "Objective": "Objective",
    "SubObjective": "Sub objective",
    "IndicatorNumber": "Indicator number",
    "Indicator": "Indicator",
    "DeskReviewAndDQAResult": "Desk Review and DQA Result",
    "SurveyResult": "Survey Result",
    "ServiceDeliveryResult": "Service Delivery Result",
    "ReasonForResult": "Reason For Result",
    "Recommendation": "Recommendation",
    "AssessmentYear": "Assessment Year :",
    "ScoreCardFileName": "Score Card"
  }
}