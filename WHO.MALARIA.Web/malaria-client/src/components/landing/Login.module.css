.loginOption {
  min-width: 300px;
  /*height: 200px;*/
  margin: 10px auto;
  display: flex;
}

.box {
  width: 48%;
  margin: 10px 10px;
  min-height: 200px;
  margin: 10px auto;
  border-radius: 7%;
  cursor: pointer;
  background-color: transparent !important;
  border: 1px solid #97E2FF !important;
  border-radius: 10px !important;
  color: #D3F3FF !important;
}

.box.active {
  background-color: #E57041 !important;
  color: white !important;
}

.box h5,
.box h6 {
  margin-left: 20px;
}

.box h5 {
  font-size: 18pt;
}

.box h6 {
  font-size: 20pt;
}

.userIcon {
  width: 100px !important;
  height: 100px !important;
}

.btnSecondary.active {
  background-color: #E57041 !important;
  color: white !important;
}

.btnSecondary.active:hover {
  background-color: #E57041;
}

/*.line:after,
.line:before {
  content: "\00a0\00a0\00a0\00a0\00a0";
  text-decoration: line-through;
}*/
.line {
  overflow: hidden;
  text-align: center;
}

.line:before,
.line:after {
  background-color: #979797;
  content: "";
  display: inline-block;
  height: 1px;
  position: relative;
  vertical-align: middle;
  width: 47%;
}

.line:before {
  right: 0.5em;
  margin-left: -50%;
}

.line:after {
  left: 0.5em;
  margin-right: -50%;
}/*# sourceMappingURL=Login.module.css.map */