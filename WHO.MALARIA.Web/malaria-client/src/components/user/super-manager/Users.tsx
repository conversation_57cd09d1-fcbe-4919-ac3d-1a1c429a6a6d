import React, { useEffect } from "react";
import { useState } from "react";
import { Button, ButtonGroup } from "@mui/material";
import { GridCellProps, GridColumnProps } from "@progress/kendo-react-grid";

import classNames from "classnames";
import { useTranslation } from "react-i18next";
import classes from "../user.module.scss";
import {
  DialogAction,
  UserCountryAccessRightsEnum,
  UserStatus,
} from "../../../models/Enums";
import { userService } from "../../../services/userService";
import EditUser from "../EditUser";
import PendingRequests from "../PendingRequests";
import Modal from "../../controls/Modal";
import { UserListModel } from "../../../models/UserModel";
import {
  PendingRequestModel,
  ResendInvitationRequestModel,
} from "../../../models/RequestModels/UserRequestModel";
import PersonIcon from "@mui/icons-material/Person";
import FlagIcon from "@mui/icons-material/Flag";
import AddUser from "../who-admin/AddUser";
import { Users as UserList } from "../Users";
import { Constants } from "../../../models/Constants";

/** Renders Super Manager users screen */
export const Users = () => {
  const { t } = useTranslation();
  document.title = t("app.UsersTitle");
  const [hover, setHover] = useState<string>("");
  const [open, setOpen] = useState<boolean>(false);
  const [openCountryAccessRequest, setOpenCountryAccessRequest] =
    useState<boolean>(false);
  const [openUserPendingRequest, setOpenUserPendingRequest] =
    useState<boolean>(false);
  const [selectedUser, setSelectedUser] = useState<UserListModel>(
    UserListModel.init()
  );
  const [users, setUsers] = useState<Array<UserListModel>>([]);
  const [showAddUserModal, setShowAddUserModal] = useState<boolean>(false);
  const toggleAddUserModal = () =>
    setShowAddUserModal((prevState: boolean) => !prevState);
  const [pendingRequests, setPendingRequests] = useState<PendingRequestModel>(
    PendingRequestModel.init()
  );
  let selectedCountryId =
    sessionStorage.getItem(Constants.SessionStorageKey.SELECTED_COUNTRY) || "";

  useEffect(() => {
    bindUsers();
    getPendingRequests();
  }, []);

  // bind users grid
  const bindUsers = () => {
    userService
      .getManagersAndViewers(selectedCountryId)
      .then((users: Array<UserListModel>) => {
        const usersData: Array<UserListModel> = users.map(
          (user: UserListModel) => {
            user.displayStatus = getUserCountryAccessStatus(user.status);
            return user;
          }
        );
        setUsers(usersData);
      });
  };

  // retrieve the pending requests
  const getPendingRequests = () => {
    userService
      .getPendingRequests(selectedCountryId)
      .then((pendingRequests: PendingRequestModel) => {
        setPendingRequests(pendingRequests);
      });
  };

  // get user country access status by enum
  const getUserCountryAccessStatus = (userStatus: number) => {
    switch (userStatus) {
      case UserCountryAccessRightsEnum.Accepted:
        return t("Common.Active");
      case UserCountryAccessRightsEnum.InvitationNotAccepted:
        return t("Common.InvitationNotAccepted");
      case UserCountryAccessRightsEnum.Pending:
        return t("Common.InvitationNotAccepted");
      case UserCountryAccessRightsEnum.Rejected:
        return t("Common.Reject");
      case UserCountryAccessRightsEnum.InActive:
        return t("Common.Inactive");
      default:
        return t("Common.Inactive");
    }
  };

  // get user country access status class name by enum
  const getUserCountryAccessStatusClassName = (userStatus: number) => {
    switch (userStatus) {
      case UserCountryAccessRightsEnum.Accepted:
        return "btn-green";
      case UserCountryAccessRightsEnum.InvitationNotAccepted:
        return "btn-orange";
      case UserCountryAccessRightsEnum.Pending:
        return "btn-orange";
      case UserCountryAccessRightsEnum.InActive:
        return "btn-grey";
      case UserCountryAccessRightsEnum.Rejected:
        return "btn-blue";
      default:
        return "btn-grey";
    }
  };

  // triggered when user clicks on Edit or Resend Invitation Email
  const changeUserStatus = (
    id: string,
    userType: number,
    name: string,
    countryId: string,
    userStatus: number
  ) => {
    if (userStatus !== UserCountryAccessRightsEnum.InvitationNotAccepted) {
      const status: number = userStatus != UserStatus.Active ? 0 : 1;
      setSelectedUser({
        ...selectedUser,
        id,
        name,
        userType,
        status,
        countryId,
      });

      setOpen(true);
    } else if (
      userStatus === UserCountryAccessRightsEnum.InvitationNotAccepted
    ) {
      const superManagerUser = new ResendInvitationRequestModel(id, countryId);
      userService.resendInvitation(superManagerUser);
    }
  };

  //get user action label based on country access status
  const getUserActionLabel = (countryAccessStatus: number) => {
    switch (countryAccessStatus) {
      case UserCountryAccessRightsEnum.InvitationNotAccepted:
        return t("UserManagement.ResendInvitation");
      default:
        return t("Common.Edit");
    }
  };

  // additional column for grid
  const additionalColumns: Array<GridColumnProps> = [
    {
      field: "status",
      title: t("UserManagement.GridColumn.Status"),
      sortable: true,
      filterable: false,
      width: "220px",
      cells: {
        data: (props: GridCellProps) => (
          <td title={props.dataItem["displayStatus"]}>
            <span
              className={getUserCountryAccessStatusClassName(
                props.dataItem["status"]
              )}
            >
              {props.dataItem["displayStatus"]}
            </span>
          </td>
        ),
      },
    },
    {
      sortable: false,
      filterable: false,
      cells: {
        data: (props: GridCellProps) => (
          <td
            className={classNames(classes.customCell)}
            title={getUserActionLabel(props.dataItem["status"])}
          >
            <Button
              className={
                hover && hover === props.dataItem["id"]
                  ? "app-btn-secondary"
                  : "app-btn-secondary app-btn-secondary-grey"
              }
              onClick={() =>
                changeUserStatus(
                  props.dataItem["id"],
                  props.dataItem["userType"],
                  props.dataItem["name"],
                  props.dataItem["countryId"],
                  props.dataItem["status"]
                )
              }
            >
              {getUserActionLabel(props.dataItem["status"])}
            </Button>
          </td>
        ),
      },
    },
  ];

  // dialog close
  const onDialogClose = (action: DialogAction) => {
    setOpen(false);
    if (action == DialogAction.Add) {
      bindUsers();
    }
  };

  return (
    <>
      <section className='page-full-section'>
        <div className='container-fluid'>
          <div className='d-flex document-title-section'>
            <h2 className='heading-title'>{t("UserManagement.AllUsers")}</h2>
            <div className='button-action-section ml-auto'>
              <div className='button-group'>
                <ButtonGroup
                  variant='text'
                  color='primary'
                  aria-label='text primary button group'
                >
                  <Button
                    onClick={() => setOpenCountryAccessRequest(true)}
                    className='btn-primary-color'
                    startIcon={<FlagIcon />}
                  >
                    {t("UserManagement.CountryAccessRequests")} (
                    {pendingRequests.countryAccessRequests.length})
                  </Button>

                  <Button
                    onClick={() => setOpenUserPendingRequest(true)}
                    className='btn-primary-color'
                    startIcon={<PersonIcon />}
                  >
                    {t("UserManagement.UserActivationRequests")} (
                    {pendingRequests.userActivationRequests.length})
                  </Button>
                </ButtonGroup>
              </div>
            </div>
          </div>

          <UserList users={users} additionalColumns={additionalColumns} />
        </div>
      </section>

      <Modal
        open={open}
        title={t("UserManagement.EditUser")}
        onEscPress={false}
        onDialogClose={() => setOpen(false)}
      >
        <EditUser
          name={selectedUser.name}
          userId={selectedUser.id}
          userType={selectedUser.userType}
          countryId={selectedUser.countryId}
          status={Boolean(selectedUser.status)}
          onDialogClose={onDialogClose}
        />
      </Modal>
      <Modal
        open={openCountryAccessRequest || openUserPendingRequest}
        title={
          openUserPendingRequest
            ? t("UserManagement.PendingRequestTitle")
            : t("UserManagement.CountryAccessRequests")
        }
        onEscPress={false}
        onDialogClose={() => {
          setOpenUserPendingRequest(false);
          setOpenCountryAccessRequest(false);
        }}
      >
        <PendingRequests
          isCountryAccess={openCountryAccessRequest}
          onCancel={() => {
            setOpenUserPendingRequest(false);
            setOpenCountryAccessRequest(false);
          }}
          getPendingRequests={getPendingRequests}
          pendingRequests={
            openUserPendingRequest
              ? pendingRequests.userActivationRequests
              : pendingRequests.countryAccessRequests
          }
          onDialogClose={onDialogClose}
        />
      </Modal>
      <Modal
        open={showAddUserModal}
        title={t("UserManagement.AddNewUser")}
        onEscPress={true}
        onDialogClose={toggleAddUserModal}
        modalClassName='app-modal-md'
      >
        <AddUser onDialogClose={onDialogClose} />
      </Modal>
    </>
  );
};
