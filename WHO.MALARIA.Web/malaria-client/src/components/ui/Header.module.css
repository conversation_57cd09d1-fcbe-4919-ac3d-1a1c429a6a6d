/****************** FONT SIZE MIXIN START ******************/
/****************** FONT SIZE MIXIN END ******************/
/****** BORDER RADIUS MIXIN START ******/
/****** BORDER RADIUS MIXIN END ******/
/****************** GLOBAL VARIABLES START ******************/
/****************** GLOBAL COLOR STARTS ******************/
/****************** GLOBAL COLOR ENDS ******************/
/****************** FONT SIZE VARIABLES START ******************/
/****************** FONT SIZE VARIABLES END ******************/
.headerWrapper {
  min-height: 65px;
}

.brandLogo path {
  fill: #fff !important;
}

a {
  text-decoration: none !important;
}

a:hover {
  text-decoration: none !important;
  cursor: pointer;
}

.navbarBrand {
  display: flex;
  align-items: center;
  font-size: 16px;
  line-height: 20px;
  letter-spacing: 0;
}
.navbarBrand a {
  color: #ffffff;
  margin-right: 15px;
  font-family: "Noto Sans", sans-serif;
  /*@include font-size($font_size_11);*/
}
.navbarBrand a:first-child {
  border-right: 1px solid #5d9acb;
}
.navbarBrand a:hover {
  color: #ffffff;
}
.navbarBrand .app-logo-text {
  font-size: 18px;
  /*@include font-size($font_size_11);*/
  line-height: 22px;
  color: #ffffff !important;
}/*# sourceMappingURL=Header.module.css.map */