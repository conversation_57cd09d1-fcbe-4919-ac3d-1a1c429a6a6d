.drawer {
  width: 25%;
  flex-shrink: 0;
  white-space: nowrap;
}

.drawerOpen {
  width: 25%;
  box-shadow: 2px 2px 10px rgba(0, 0, 0, 0.25) !important;
  border-right: none !important;
}
.drawerOpen .drawerToggle {
  left: 25%;
}

.drawerToggle {
  position: fixed !important;
  top: 15%;
  width: 20px;
  height: 40px;
  z-index: 1500;
  background-color: #fff !important;
  border-radius: 0px;
  cursor: pointer;
  vertical-align: middle;
  text-align: center;
  /* border: 2px solid red; */
  padding: 10px 0px;
  border-top-right-radius: 5px;
  border-bottom-right-radius: 5px;
  box-shadow: 2px 2px 2px rgba(0, 0, 0, 0.25) !important;
}
.drawerToggle svg {
  margin-left: -2px;
  color: #686868;
}

.drawerClose {
  overflow-x: hidden;
  width: 15px;
  box-shadow: 2px 2px 10px rgba(0, 0, 0, 0.25) !important;
  border-right: none !important;
  overflow-y: hidden !important;
}
.drawerClose .drawerToggle {
  left: 15px;
}

.content {
  flex-grow: 1;
  padding: 20px;
}/*# sourceMappingURL=guide.module.css.map */