import { Button } from "@mui/material";
import classNames from "classnames";
import React, { useEffect, useRef, ChangeEvent, useState } from "react";
import { useTranslation } from "react-i18next";
import Checkbox from "../../../../../../controls/Checkbox";
import TextBox from "../../../../../../controls/TextBox";
import TableRow from "../../../responses/TableRow";
import TableCell from "../../../responses/TableCell";
import TableBody from "../../../responses/TableBody";
import { Response_1 } from "../../../../../../../models/DeskReview/Objective_1/Indicator_1_3_3/Response_1";
import useIndicatorResponseCapture from "../../../responses/useIndicatorResponseCapture";
import useCalculation from "../../../responses/useCalculation";
import useFormValidation from "../../../../../../common/useFormValidation";
import ValidationRules from "./ValidationRules";
import { useSelector } from "react-redux";
import { IValidationRuleProvider } from "../../../../../../../models/ValidationRuleModel";
import { CannotBeAssessedReasonValidationRule } from "../../../CannotBeAssessedReasonValidationRule";
import { MetNotMetStatus } from "../../../MetNotMetStatus";
import { MetNotMetEnum } from "../../../../../../../models/Enums";
import SaveFinalizeButton from "../../../../../../assessments/SaveFinalizeButton";
import useCalculationProportionalRate from "../../../useCalculationProportionalRate";

// Function to check if at least one health system level is completed
const isAtLeastOneHealthSystemLevelCompleted = (response: Response_1) => {
  return ["nationalLevel", "regionalLevel", "districtLevel"].some(level => {
    const levelData = response[level as keyof Response_1] as any;
    if (!levelData) return false;

    const occured = levelData.noOfMeetingsOccured;
    const expected = levelData.noOfMeetingsExpected;

    return (
      occured !== undefined &&
      occured !== "" &&
      occured !== null &&
      expected !== undefined &&
      expected !== "" &&
      expected !== null
    );
  });
};

// Modified validation rules
const ModifiedValidationRules = { ...ValidationRules };

// Make all health system fields optional
const optionalFieldKeys = [
  "nationalLevel.noOfMeetingsOccured",
  "nationalLevel.noOfMeetingsExpected",
  "regionalLevel.noOfMeetingsOccured",
  "regionalLevel.noOfMeetingsExpected",
  "districtLevel.noOfMeetingsOccured",
  "districtLevel.noOfMeetingsExpected",
];

optionalFieldKeys.forEach(key => {
  if (ModifiedValidationRules[key]) {
    ModifiedValidationRules[key] = {
      ...ModifiedValidationRules[key],
      condition: "",
      errorMessage: "",
    };
  }
});

/** Renders the response for indicator 1.3.3 */
function Indicator_1_3_3_Response() {
  const { t } = useTranslation(["indicators-responses"]);
  const tr = useTranslation();

  document.title = t(
    "indicators-responses:app:DR_Objective_1_Indicator_1_3_3_Title"
  );
  const { calculatePercentage } = useCalculation();
  const { calculateProportionRateProperty, isProportionRateValid } =
    useCalculationProportionalRate();

  /** Excluded property that are not used in Array Map */
  const excludedProperties: Array<string> = [
    "cannotBeAssessed",
    "cannotBeAssessedReason",
    "calculateRate",
    "healthSystemLevelValidationRuleKey",
    "metNotMetStatus",
  ];

  const validationRulesRef = useRef<IValidationRuleProvider>(
    ModifiedValidationRules
  );

  const validate = useFormValidation(validationRulesRef.current);

  const {
    response,
    onChange,
    onCannotBeAssessed,
    onChangeWithKey,
    getResponse,
    onSave,
    onFinalize,
    onValueChange,
    setTrueFlagOnFinalizeButtonClick,
  } = useIndicatorResponseCapture<Response_1>(Response_1.init(), validate);

  const errors = useSelector((state: any) => state.error);

  // State for custom denominator validation errors
  const [customErrors, setCustomErrors] = useState<Record<string, string>>({});

  // State for health system level error
  const [healthSystemLevelError, setHealthSystemLevelError] =
    useState<string>("");

  // Function to validate denominator when numerator is entered
  const validateDenominator = (level: string, occured: any, expected: any) => {
    const key = `${level}.noOfMeetingsExpected`;

    // Reset error if previously set
    if (customErrors[key]) {
      setCustomErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[key];
        return newErrors;
      });
    }

    // Check if numerator is entered but denominator is missing or invalid
    if (
      occured !== undefined &&
      occured !== "" &&
      occured !== null &&
      (expected === undefined ||
        expected === "" ||
        expected === null ||
        Number(expected) <= 0)
    ) {
      setCustomErrors(prev => ({
        ...prev,
        [key]: tr.t(ValidationRules[key].errorMessage as string),
      }));
      return false;
    }

    return true;
  };

  const onResponseFinalize = () => {
    setTrueFlagOnFinalizeButtonClick();
    // Clear previous custom errors
    setCustomErrors({});
    setHealthSystemLevelError("");

    // Validate all denominators
    let isValid = true;
    // Validate national level
    isValid =
      validateDenominator(
        "nationalLevel",
        response.nationalLevel?.noOfMeetingsOccured,
        response.nationalLevel?.noOfMeetingsExpected
      ) && isValid;
    // Validate regional level
    isValid =
      validateDenominator(
        "regionalLevel",
        response.regionalLevel?.noOfMeetingsOccured,
        response.regionalLevel?.noOfMeetingsExpected
      ) && isValid;
    // Validate district level
    isValid =
      validateDenominator(
        "districtLevel",
        response.districtLevel?.noOfMeetingsOccured,
        response.districtLevel?.noOfMeetingsExpected
      ) && isValid;
      
    // Check all validations
    const isFormValid = validate(response) && isValid;
    const isHealthSystemValid =
      response.cannotBeAssessed ||
      isAtLeastOneHealthSystemLevelCompleted(response);
    if (!isHealthSystemValid && isValid) {
      setHealthSystemLevelError(
        t(
          "indicators-responses:DRObjective_1_Responses:Indicator_1_3_3:ResponseError"
        ) || ""
      );
    }
    if (isFormValid && isHealthSystemValid && isProportionRateValid()) {
      onFinalize();
      setCustomErrors({});
      setHealthSystemLevelError("");
    }
  };

  useEffect(() => {
    getResponse();
  }, []);

  useEffect(() => {
    validationRulesRef.current =
      response?.cannotBeAssessed === true
        ? CannotBeAssessedReasonValidationRule
        : ModifiedValidationRules;
  }, [response?.cannotBeAssessed]);

  //Triggers onChange of cannotBeAssessed checkbox
  const onCannotBeAssessedChange = (evt: ChangeEvent<HTMLInputElement>) => {
    // Reset custom errors when cannot be assessed is toggled
    setCustomErrors({});

    validationRulesRef.current = evt.currentTarget.checked
      ? CannotBeAssessedReasonValidationRule
      : ModifiedValidationRules;

    onCannotBeAssessed(evt);
  };

  const rowsData: any = {
    nationalLevel: t(
      "indicators-responses:DRObjective_1_Responses:Indicator_1_3_3:NationalLevel"
    ),
    regionalLevel: t(
      "indicators-responses:DRObjective_1_Responses:Indicator_1_3_3:RegionalLevel"
    ),
    districtLevel: t(
      "indicators-responses:DRObjective_1_Responses:Indicator_1_3_3:DistrictLevel"
    ),
  };

  // Function to handle field changes
  const handleFieldChange = (
    e: React.ChangeEvent<HTMLInputElement>,
    modelKeyName: string
  ) => {
    onChangeWithKey(e, modelKeyName);
    getMetNotMetStatus();

    // Clear custom error for this denominator field if user starts typing
    if (e.target.name === "noOfMeetingsExpected") {
      const key = `${modelKeyName}.noOfMeetingsExpected`;
      if (customErrors[key]) {
        setCustomErrors(prev => {
          const newErrors = { ...prev };
          delete newErrors[key];
          return newErrors;
        });
      }
    }

    // Clear healthSystemLevelError if at least one health system level is now completed
    if (
      e.target.name === "noOfMeetingsOccured" ||
      e.target.name === "noOfMeetingsExpected"
    ) {
      const updatedResponse = {
        ...response,
        [modelKeyName]: {
          ...response[modelKeyName],
          [e.target.name]: e.target.value,
        },
      };
      if (isAtLeastOneHealthSystemLevelCompleted(updatedResponse)) {
        setHealthSystemLevelError("");
      }
    }
  };

  //Check condition for met and not met and return status
  const getMetNotMetStatus = () => {
    const evidencePercentage = calculatePercentage(
      response?.nationalLevel?.noOfMeetingsOccured,
      response?.nationalLevel?.noOfMeetingsExpected
    );

    onValueChange(
      "metNotMetStatus",
      evidencePercentage >= 80
        ? MetNotMetEnum.Met
        : evidencePercentage < 50
          ? MetNotMetEnum.NotMet
          : MetNotMetEnum.PartiallyMet
    );
  };

  useEffect(() => {
    getMetNotMetStatus();
  }, [
    response?.nationalLevel?.noOfMeetingsOccured,
    response?.nationalLevel?.noOfMeetingsExpected,
  ]);

  return (
    <>
      <MetNotMetStatus
        status={response.metNotMetStatus}
        tooltip={t(
          "indicators-responses:DRObjective_1_Responses:Indicator_1_3_3:MetNotMetTooltip"
        )}
      />
      <div className='response-assess-wrapper'>
        <Checkbox
          id='cannotBeAssessed'
          name='cannotBeAssessed'
          label={t("indicators-responses:Common:IndicatorNoAssess")}
          onChange={onCannotBeAssessedChange}
          checked={response?.cannotBeAssessed}
        />
      </div>

      {!response.cannotBeAssessed ? (
        <div className='response-wrapper'>
          <p className='fw-lighter'>
            {t(
              "indicators-responses:DRObjective_1_Responses:Indicator_1_3_3:ResponseDesc"
            )}
          </p>
          <p>
            {t(
              "indicators-responses:DRObjective_1_Responses:Indicator_1_3_3:RecordMeetingPreviousYear"
            )}
          </p>
          <p className='mt-3 fst-italic'>
            {t(
              "indicators-responses:DRObjective_1_Responses:Indicator_1_3_3:NoteToCompleteTheAssessment"
            )}
          </p>
          <p>
            {t(
              "indicators-responses:DRObjective_1_Responses:Indicator_1_3_3:MeetingsExpectedDesc"
            )}
          </p>
          {/*Show error message if at least one of the health system level is not completed*/}
          {healthSystemLevelError && (
            <span className='Mui-error d-flex mb-2'>
              *{healthSystemLevelError}
            </span>
          )}
          <div className='mt-3'>
            <table width='60%' className='app-table'>
              <thead>
                <th>
                  <div className='fw-bold'>
                    {t(
                      "indicators-responses:DRObjective_1_Responses:Indicator_1_3_3:HealthSystemLevel"
                    )}
                  </div>
                </th>
                <th>
                  <div className='fw-bold'>
                    {t(
                      "indicators-responses:DRObjective_1_Responses:Indicator_1_3_3:MeetingsOccurred"
                    )}
                  </div>
                </th>
                <th>
                  <div className='fw-bold'>
                    {t(
                      "indicators-responses:DRObjective_1_Responses:Indicator_1_3_3:MeetingsExpected"
                    )}
                  </div>
                </th>
                <th>
                  <div className='fw-bold'>
                    {t(
                      "indicators-responses:DRObjective_1_Responses:Indicator_1_3_3:Rate"
                    )}
                  </div>
                </th>
              </thead>
              <TableBody>
                <>
                  {Object.keys(response)
                    .filter(key => !excludedProperties.includes(key))
                    .map((modelKeyName: string, index: number) => (
                      <TableRow key={`row_${modelKeyName}_${index}`}>
                        <>
                          <TableCell>{rowsData[modelKeyName]}</TableCell>
                          <TableCell>
                            <TextBox
                              id='noOfMeetingsOccured'
                              type='number'
                              name='noOfMeetingsOccured'
                              maxLength={3}
                              fullWidth
                              inputProps={{
                                max: 100,
                                min: 0,
                              }}
                              value={
                                response[modelKeyName]?.noOfMeetingsOccured
                              }
                              onChange={(
                                e: React.ChangeEvent<HTMLInputElement>
                              ) => handleFieldChange(e, modelKeyName)}
                              error={
                                !!errors[
                                  `${modelKeyName}.noOfMeetingsOccured`
                                ] ||
                                !!customErrors[
                                  `${modelKeyName}.noOfMeetingsOccured`
                                ]
                              }
                              helperText={
                                errors[`${modelKeyName}.noOfMeetingsOccured`] ||
                                customErrors[
                                  `${modelKeyName}.noOfMeetingsOccured`
                                ]
                              }
                            />
                          </TableCell>
                          <TableCell>
                            <TextBox
                              id='noOfMeetingsExpected'
                              type='number'
                              maxLength={3}
                              fullWidth
                              inputProps={{
                                max: 100,
                                min: 0,
                              }}
                              name='noOfMeetingsExpected'
                              value={
                                response[modelKeyName]?.noOfMeetingsExpected
                              }
                              onChange={(
                                e: React.ChangeEvent<HTMLInputElement>
                              ) => handleFieldChange(e, modelKeyName)}
                              error={
                                !!errors[
                                  `${modelKeyName}.noOfMeetingsExpected`
                                ] ||
                                !!customErrors[
                                  `${modelKeyName}.noOfMeetingsExpected`
                                ]
                              }
                              helperText={
                                errors[
                                  `${modelKeyName}.noOfMeetingsExpected`
                                ] ||
                                customErrors[
                                  `${modelKeyName}.noOfMeetingsExpected`
                                ] ||
                                ""
                              }
                            />
                          </TableCell>

                          <TableCell>
                            <label>
                              {calculateProportionRateProperty(
                                response[modelKeyName]?.noOfMeetingsOccured,
                                response[modelKeyName]?.noOfMeetingsExpected,
                                response
                              )}
                            </label>
                          </TableCell>
                        </>
                      </TableRow>
                    ))}
                </>
              </TableBody>
            </table>
          </div>
        </div>
      ) : (
        <div className='response-wrapper d-flex'>
          <TextBox
            id='cannotBeAssessedReason'
            name='cannotBeAssessedReason'
            label={t("indicators-responses:Common:IndicatorNoAssessReasons")}
            multiline
            rows={10}
            variant='outlined'
            fullWidth
            value={response?.cannotBeAssessedReason || ""}
            onChange={onChange}
            error={
              errors["cannotBeAssessedReason"] &&
              errors["cannotBeAssessedReason"]
            }
            helperText={
              errors["cannotBeAssessedReason"] &&
              errors["cannotBeAssessedReason"]
            }
          />
        </div>
      )}
      <SaveFinalizeButton onSave={onSave} onFinalize={onResponseFinalize} />
    </>
  );
}

export default Indicator_1_3_3_Response;
