﻿import MultiSelectModel from "../../../../../../../models/MultiSelectModel";
import TextBox from "../../../../../../controls/TextBox";
import Table from "../../../responses/Table";
import TableBody from "../../../responses/TableBody";
import TableCell from "../../../responses/TableCell";
import TableHeader from "../../../responses/TableHeader";
import TableRow from "../../../responses/TableRow";
import RadioButtonGroup from "../../../../../../controls/RadioButtonGroup";
import { useTranslation } from "react-i18next";
import { ChangeEvent, useEffect, useRef } from "react";
import TableFooter from "../../../responses/TableFooter";
import Checkbox from "../../../../../../controls/Checkbox";
import useCalculation from "../../../responses/useCalculation";
import { useLocation } from "react-router-dom";
import { ChecklistVariableModel } from "../../../../../../../models/DeskReview/ChecklistVariableModel";
import ValidationRules from "./ValidationRules";
import useFormValidation from "../../../../../../common/useFormValidation";
import { Response_1, TransmitMalariaVariable } from "../../../../../../../models/DeskReview/Objective_3/Indicator_3_3_2/Response_1";
import useIndicatorResponseCapture from "../../../responses/useIndicatorResponseCapture";
import { useSelector } from "react-redux";
import useChecklistVariable from "../../../responses/useChecklistVariable";
import { IValidationRuleProvider } from '../../../../../../../models/ValidationRuleModel';
import { CannotBeAssessedReasonValidationRule } from "../../../CannotBeAssessedReasonValidationRule";
import SaveFinalizeButton from "../../../../../../assessments/SaveFinalizeButton";

/** Renders the response for indicator 3.3.2 */
function Indicator_3_3_2_Response() {
    const { t } = useTranslation(["indicators-responses"]);
    document.title = t("indicators-responses:app:DR_Objective_3_Indicator_3_3_2_Title");
    const location: any = useLocation();
    const strategyId: string = location?.state?.strategyId;
    const { calculatePercentageOfYesNo } = useCalculation();
    const checkListVariables = useChecklistVariable(strategyId);

    //Holds the validation rules and it gets changed when cannot be assessed is set to true.
    const validationRulesRef = useRef<IValidationRuleProvider>(ValidationRules);

    const validate = useFormValidation(validationRulesRef.current);

    const {
        response,
        onChange,
        onCannotBeAssessed,
        onSave,
        onFinalize,
        getResponse,
        onValueChange,
        setTrueFlagOnFinalizeButtonClick
    } = useIndicatorResponseCapture<Response_1>(Response_1.init(strategyId), validate);

    const errors = useSelector((state: any) => state.error);

    // Variable for checking the condition for Proportional Calculation Rate the Rate should be between 0 to 100
    let isProportionRateValid: boolean = true;

    // triggers on click of finalize button, performs validations and then action is performed
    const onResponseFinalize = () => {
        setTrueFlagOnFinalizeButtonClick();
        const isFormValid = validate(response);
        if (isFormValid && isProportionRateValid) {
            onFinalize();
        }
    };

    useEffect(() => {
        getResponse();
    }, []);

    useEffect(() => {
        if (checkListVariables) {
            onValueChange("checkListVariablesCount", checkListVariables.length);
        }
    }, [checkListVariables]);

    useEffect(() => {
        validationRulesRef.current =
            response?.cannotBeAssessed === true
                ? CannotBeAssessedReasonValidationRule
                : ValidationRules;

    }, [response?.cannotBeAssessed]);

    //Triggers onChange of cannotBeAssessed checkbox 
    const onCannotBeAssessedChange = (evt: ChangeEvent<HTMLInputElement>) => {
        //If indicator is can not be assessed then the other validation rules doesn't make sense except for can not be assessed reason rule.
        //If we do not change the validation rules and use all the rules in useFormValidation hook then that hook will validate on all the rules
        //for all the response data which is not needed and this behavior prevents form submitting even user has added the data in cannot be assessed
        //reason field. Hence, to prevent it we are setting validation rule only for can not be assessed reason field.
        validationRulesRef.current =
            evt.currentTarget.checked
                ? CannotBeAssessedReasonValidationRule
                : ValidationRules;

        onCannotBeAssessed(evt);
    }

    // Triggered whenever the control values are changed and update response
    const onRadioButtonValueChange = (
        fieldName: string,
        value: any,
        variableId: string
    ) => {
        value = value === "false" ? false : true;
        const transmitMalariaVariables = response.transmitMalariaVariables ? [...response.transmitMalariaVariables] : [];
        const variableData: TransmitMalariaVariable | undefined = transmitMalariaVariables.find((v: TransmitMalariaVariable) => v.variableId === variableId);

        if (variableData) {
            variableData[fieldName] = value;
        } else {
            const transmitMalariaVariable = new TransmitMalariaVariable(variableId);
            transmitMalariaVariables.push({ ...transmitMalariaVariable, [fieldName]: value })
        }

        onValueChange("transmitMalariaVariables", transmitMalariaVariables);
    };

    const headersMain = [
        {
            field: "variables",
            label: `${t("indicators-responses:DRObjective_3_Responses:Indicator_3_3_2:Variables")}`
        },
        {
            field: "recordedInSourceDocuments",
            label: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_3_2:RecordedInSourceDocuments"
            ),
        },
        {
            field: "disagregation",
            label: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_3_2:Disagregation"
            ),
        },
        { field: "", label: "" },
        { field: "", label: "" },
        { field: "", label: "" },
        { field: "", label: "" },
        { field: "", label: "" },
        { field: "", label: "" },
    ];

    const headersSecondary = [
        { field: "", label: " " },
        { field: "", label: " " },

        {
            field: "under5",
            label: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_3_2:Under5"
            ),
        },
        {
            field: "over5",
            label: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_3_2:Over5"
            ),
        },
        {
            field: "gender",
            label: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_3_2:Sex"
            ),
        },
        {
            field: "pregnantWoman",
            label: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_3_2:PregnantWoman"
            ),
        },
        {
            field: "healthSector",
            label: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_3_2:HealthSector"
            ),
        },
        {
            field: "geography",
            label: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_3_2:Geography"
            ),
        },
        {
            field: "other",
            label: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_3_2:Other"
            ),
        },
    ];

    //Method creates an array of properties that have checked "Yes" and returns percentage of it
    const calculateRecordedInSourceYesNoPercentage = () => {
        const propertyArray: boolean[] = checkListVariables?.map((checkListVariable: ChecklistVariableModel) => (getTransmitMalariaVariableValue(checkListVariable.variableId, "recordedInSource"))
        );

        return calculatePercentageOfYesNo(propertyArray);
    };

    //Check condition for proportion calculation rate and validate and shows message if value less than 0 or greater than 100 
    const calculateRecordedInSourcePercentage = () => {
        const proportionRateValue = calculateRecordedInSourceYesNoPercentage();
        const proportionRateExceptionContent =
            <span className="Mui-error d-flex mb-2">
                * {t("indicators-responses:Common:ResponseProportionError")}
            </span>

        if (proportionRateValue >= 0 && proportionRateValue <= 100) {
            isProportionRateValid = true;
            return proportionRateValue + '%';
        }
        isProportionRateValid = false;
        return proportionRateExceptionContent;
    }

    // get the TransmitMalariaVariable based on the variableId and bind it on change event
    const getTransmitMalariaVariableValue = (variableId: string, fieldName: string) => {
        const transmitVariable = (response.transmitMalariaVariables?.find((v: TransmitMalariaVariable) => v.variableId === variableId));
        if (!transmitVariable || transmitVariable[fieldName] === undefined) return "";

        return transmitVariable[fieldName];
    }

    return (
        <>
            <div className="response-assess-wrapper">
                <Checkbox
                    id="cannotBeAssessed"
                    name="cannotBeAssessed"
                    label={t("indicators-responses:Common:IndicatorNoAssess")}
                    onChange={onCannotBeAssessedChange}
                    checked={response?.cannotBeAssessed}
                />
            </div>
            {!response?.cannotBeAssessed ? (
                <div className="response-wrapper">
                    <p>
                        {t(
                            "indicators-responses:DRObjective_3_Responses:Indicator_3_3_2:GenomicResponseDesc"
                        )}
                    </p>
                    <p className="fst-italic">
                        {t(
                            "indicators-responses:DRObjective_3_Responses:Indicator_3_3_2:ResponseTitle"
                        )}
                    </p>
                    <div>
                        {
                            //Show error message if user does not select 'Yes' or 'No' for 'Indicator Checklist' and 'Disaggregations' for variables
                            !!Object.keys(errors).length &&
                            <span className="Mui-error d-flex mb-2">
                                * {t("indicators-responses:DRObjective_3_Responses:Indicator_3_3_2:ResponseError")}
                            </span>
                        }
                        <Table>
                            <>
                                <TableHeader
                                    headers={headersMain.map((header: any) => header.label)}
                                />
                                <TableHeader
                                    headers={headersSecondary.map((header: any) => header.label)}
                                />
                                <TableBody>
                                    <>
                                        {
                                            checkListVariables?.map((checkListVariable: ChecklistVariableModel, index: number) => (
                                                <TableRow key={`row_${checkListVariable.variableId}_${index}`}>
                                                    <>
                                                        <TableCell>
                                                            <p className="d-flex mb-0">
                                                                <span className="d-inline-flex me-2">{index + 1} </span> {checkListVariable.name}</p>
                                                        </TableCell>
                                                        <TableCell>
                                                            <RadioButtonGroup
                                                                id="recordedInSource"
                                                                name="recordedInSource"
                                                                row
                                                                color="primary"
                                                                options={[
                                                                    new MultiSelectModel(
                                                                        true,
                                                                        t("indicators-responses:Common:Yes"),
                                                                        !checkListVariable.reported
                                                                    ),
                                                                    new MultiSelectModel(
                                                                        false,
                                                                        t("indicators-responses:Common:No"),
                                                                        !checkListVariable.reported
                                                                    ),
                                                                ]}
                                                                value={getTransmitMalariaVariableValue(checkListVariable.variableId, "recordedInSource")}
                                                                onChange={(
                                                                    e: React.ChangeEvent<HTMLInputElement>
                                                                ) =>
                                                                    onRadioButtonValueChange(
                                                                        "recordedInSource",
                                                                        e.currentTarget.value,
                                                                        checkListVariable.variableId
                                                                    )}
                                                                error={errors[`transmitMalariaVariables[${index}].recordedInSource`] && errors[`transmitMalariaVariables[${index}].recordedInSource`]}
                                                            />
                                                        </TableCell>
                                                        <TableCell>
                                                            <RadioButtonGroup
                                                                id="disagregation"
                                                                name="disagregation"
                                                                color="primary"
                                                                options={[
                                                                    new MultiSelectModel(
                                                                        true,
                                                                        t("indicators-responses:Common:Yes"),
                                                                        !checkListVariable.underFive
                                                                    ),
                                                                    new MultiSelectModel(
                                                                        false,
                                                                        t("indicators-responses:Common:No"),
                                                                        !checkListVariable.underFive
                                                                    ),
                                                                ]}
                                                                value={getTransmitMalariaVariableValue(checkListVariable.variableId, "disagregation")}
                                                                onChange={(
                                                                    e: React.ChangeEvent<HTMLInputElement>
                                                                ) => onRadioButtonValueChange(
                                                                    "disagregation",
                                                                    e.currentTarget.value,
                                                                    checkListVariable.variableId
                                                                )}
                                                                error={errors[`transmitMalariaVariables[${index}].disagregation`] && errors[`transmitMalariaVariables[${index}].disagregation`]}
                                                            />
                                                        </TableCell>
                                                        <TableCell>
                                                            <RadioButtonGroup
                                                                id="over5"
                                                                name="over5"
                                                                row
                                                                color="primary"
                                                                options={[
                                                                    new MultiSelectModel(
                                                                        true,
                                                                        t("indicators-responses:Common:Yes"),
                                                                        !checkListVariable.overFive
                                                                    ),
                                                                    new MultiSelectModel(
                                                                        false,
                                                                        t("indicators-responses:Common:No"),
                                                                        !checkListVariable.overFive
                                                                    ),
                                                                ]}
                                                                value={getTransmitMalariaVariableValue(checkListVariable.variableId, "over5")}
                                                                onChange={(
                                                                    e: React.ChangeEvent<HTMLInputElement>
                                                                ) => onRadioButtonValueChange(
                                                                    "over5",
                                                                    e.currentTarget.value,
                                                                    checkListVariable.variableId
                                                                )}
                                                                error={errors[`transmitMalariaVariables[${index}].over5`] && errors[`transmitMalariaVariables[${index}].over5`]}
                                                            />
                                                        </TableCell>
                                                        <TableCell>
                                                            <RadioButtonGroup
                                                                id="gender"
                                                                name="gender"
                                                                color="primary"
                                                                options={[
                                                                    new MultiSelectModel(
                                                                        true,
                                                                        t("indicators-responses:Common:Yes"),
                                                                        !checkListVariable.gender
                                                                    ),
                                                                    new MultiSelectModel(
                                                                        false,
                                                                        t("indicators-responses:Common:No"),
                                                                        !checkListVariable.gender
                                                                    ),
                                                                ]}
                                                                value={getTransmitMalariaVariableValue(checkListVariable.variableId, "gender")}
                                                                onChange={(
                                                                    e: React.ChangeEvent<HTMLInputElement>
                                                                ) => onRadioButtonValueChange(
                                                                    "gender",
                                                                    e.currentTarget.value,
                                                                    checkListVariable.variableId
                                                                )}
                                                                error={errors[`transmitMalariaVariables[${index}].gender`] && errors[`transmitMalariaVariables[${index}].gender`]}
                                                            />
                                                        </TableCell>
                                                        <TableCell>
                                                            <RadioButtonGroup
                                                                id="pregnantWomen"
                                                                name="pregnantWomen"
                                                                row
                                                                color="primary"
                                                                options={[
                                                                    new MultiSelectModel(
                                                                        true,
                                                                        t("indicators-responses:Common:Yes"),
                                                                        !checkListVariable.pregnantWoman
                                                                    ),
                                                                    new MultiSelectModel(
                                                                        false,
                                                                        t("indicators-responses:Common:No"),
                                                                        !checkListVariable.pregnantWoman
                                                                    ),
                                                                ]}
                                                                value={getTransmitMalariaVariableValue(checkListVariable.variableId, "pregnantWomen")}
                                                                onChange={(
                                                                    e: React.ChangeEvent<HTMLInputElement>
                                                                ) => onRadioButtonValueChange(
                                                                    "pregnantWomen",
                                                                    e.currentTarget.value,
                                                                    checkListVariable.variableId
                                                                )}
                                                                error={errors[`transmitMalariaVariables[${index}].pregnantWomen`] && errors[`transmitMalariaVariables[${index}].pregnantWomen`]}
                                                            />
                                                        </TableCell>
                                                        <TableCell>
                                                            <RadioButtonGroup
                                                                id="healthSector"
                                                                name="healthSector"
                                                                color="primary"
                                                                options={[
                                                                    new MultiSelectModel(
                                                                        true,
                                                                        t("indicators-responses:Common:Yes"),
                                                                        !checkListVariable.healthSector
                                                                    ),
                                                                    new MultiSelectModel(
                                                                        false,
                                                                        t("indicators-responses:Common:No"),
                                                                        !checkListVariable.healthSector
                                                                    ),
                                                                ]}
                                                                value={getTransmitMalariaVariableValue(checkListVariable.variableId, "healthSector")}
                                                                onChange={(
                                                                    e: React.ChangeEvent<HTMLInputElement>
                                                                ) => onRadioButtonValueChange(
                                                                    "healthSector",
                                                                    e.currentTarget.value,
                                                                    checkListVariable.variableId
                                                                )}
                                                                error={errors[`transmitMalariaVariables[${index}].healthSector`] && errors[`transmitMalariaVariables[${index}].healthSector`]}
                                                            />
                                                        </TableCell>
                                                        <TableCell>
                                                            <RadioButtonGroup
                                                                id="geography"
                                                                name="geography"
                                                                row
                                                                color="primary"
                                                                options={[
                                                                    new MultiSelectModel(
                                                                        true,
                                                                        t("indicators-responses:Common:Yes"),
                                                                        !checkListVariable.geography
                                                                    ),
                                                                    new MultiSelectModel(
                                                                        false,
                                                                        t("indicators-responses:Common:No"),
                                                                        !checkListVariable.geography
                                                                    ),
                                                                ]}
                                                                value={getTransmitMalariaVariableValue(checkListVariable.variableId, "geography")}
                                                                onChange={(
                                                                    e: React.ChangeEvent<HTMLInputElement>
                                                                ) => onRadioButtonValueChange(
                                                                    "geography",
                                                                    e.currentTarget.value,
                                                                    checkListVariable.variableId
                                                                )}
                                                                error={errors[`transmitMalariaVariables[${index}].geography`] && errors[`transmitMalariaVariables[${index}].geography`]}
                                                            />
                                                        </TableCell>
                                                        <TableCell>
                                                            <RadioButtonGroup
                                                                id="other"
                                                                name="other"
                                                                color="primary"
                                                                options={[
                                                                    new MultiSelectModel(
                                                                        true,
                                                                        t("indicators-responses:Common:Yes"),
                                                                        !checkListVariable.other
                                                                    ),
                                                                    new MultiSelectModel(
                                                                        false,
                                                                        t("indicators-responses:Common:No"),
                                                                        !checkListVariable.other
                                                                    ),
                                                                ]}
                                                                value={getTransmitMalariaVariableValue(checkListVariable.variableId, "other")}
                                                                onChange={(
                                                                    e: React.ChangeEvent<HTMLInputElement>
                                                                ) => onRadioButtonValueChange(
                                                                    "other",
                                                                    e.currentTarget.value,
                                                                    checkListVariable.variableId
                                                                )}
                                                                error={errors[`transmitMalariaVariables[${index}].other`] && errors[`transmitMalariaVariables[${index}].other`]}
                                                            />
                                                        </TableCell>
                                                    </>
                                                </TableRow>
                                            ))}
                                    </>
                                </TableBody>

                                <TableFooter>
                                    <>
                                        <TableCell>
                                            <span>
                                                {t(
                                                    "indicators-responses:DRObjective_3_Responses:Indicator_3_3_2:TableFooterTitle"
                                                )}
                                            </span>
                                        </TableCell>

                                        <TableCell colSpan={10}>
                                            <span>{calculateRecordedInSourcePercentage()}</span>
                                        </TableCell>
                                    </>
                                </TableFooter>
                            </>
                        </Table>
                    </div>
                </div>
            ) : (
                <div className="response-wrapper d-flex">
                    <TextBox
                        id="cannotBeAssessedReason"
                        name="cannotBeAssessedReason"
                        label={t("indicators-responses:Common:IndicatorNoAssessReasons")}
                        multiline
                        rows={10}
                        variant="outlined"
                        fullWidth
                        value={response?.cannotBeAssessedReason}
                        onChange={onChange}
                        error={
                            errors["cannotBeAssessedReason"] &&
                            errors["cannotBeAssessedReason"]
                        }
                        helperText={
                            errors["cannotBeAssessedReason"] &&
                            errors["cannotBeAssessedReason"]
                        }
                    />
                </div>
            )}

            <SaveFinalizeButton onSave={onSave} onFinalize={onResponseFinalize} />

        </>
    );
}

export default Indicator_3_3_2_Response;
