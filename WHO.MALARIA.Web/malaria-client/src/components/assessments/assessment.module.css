.tabWrapper {
  width: auto;
}

.progressWrapper {
  width: 100%;
}

.stepWrapper {
  min-width: 55% !important;
  margin: 0 auto;
  width: -moz-fit-content;
  width: fit-content;
}

.categoryWrapper {
  min-height: 400px;
}

.stepWrapper svg {
  fill: #008dc3;
}

.br_b {
  border-bottom: 1px solid #ddd;
}

/** Progressbar */
.primary {
  color: green;
}

.noRecordFound {
  text-align: center;
}

fieldset:disabled {
  color: #a7a3a3;
}

.alert {
  color: #dd1f1f;
}

.BackdropProps {
  background-color: transparent !important;
  box-shadow: none;
}/*# sourceMappingURL=assessment.module.css.map */