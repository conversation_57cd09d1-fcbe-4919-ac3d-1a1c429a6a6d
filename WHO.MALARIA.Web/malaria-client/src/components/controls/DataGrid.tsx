import React, { useEffect, useState } from 'react';
import { SortDescriptor } from '@progress/kendo-data-query';
import {
  Grid,
  GridColumn as Column,
  GridColumnProps,
  GridHeaderCellProps,
  GridProps,
  GridSortChangeEvent,
  GridRowProps,
  GridCustomRowProps,
} from '@progress/kendo-react-grid';
import GridHeaderCell from './GridHeaderCell';



type DataGridProps = GridProps & {
  columns: Array<GridColumnProps>;
  className?: string;
  defaultHeight?: string;
  initialSort?: Array<SortDescriptor>;
  onSortChange?: (e: GridSortChangeEvent) => void;
  onFilterChange?: (e: any) => void;
  filterable?: boolean;
  hasActionBtn?: boolean;
  rowRender?: (
    trElement: React.ReactElement<HTMLTableRowElement>,
    rowProps: GridRowProps
  ) => React.ReactElement;
};

const DataGrid = (props: DataGridProps) => {
  const {
    columns,
    defaultHeight,
    data,
    total,
    pageable,
    hasActionBtn,
    rowRender,
    take,
    skip,
    onPageChange,
    ...restProps
  } = props;

  const [openFilter, setOpenFilter] = useState(false);

  const onFilterClick = () => {
    setOpenFilter(prev => !prev);
  };

  const cells = {
    headerCell: (headerCellProps: GridHeaderCellProps) => (
      <GridHeaderCell
        open={openFilter}
        data={data}
        hasActionBtn={hasActionBtn}
        onFilterClick={onFilterClick}
        {...headerCellProps}
        {...props}
      />
    ),
  };

  const rows = rowRender
    ? {
        data: (rowProps: GridCustomRowProps) => {
          const defaultTr = <tr {...rowProps.trProps}>{rowProps.children}</tr>;
          return rowRender(defaultTr, rowProps);
        },
      }
    : undefined;

  return (
      <Grid
        style={{ height: defaultHeight || 'auto' }}
        filterable={openFilter}
        pageable={pageable ? { buttonCount: 4, pageSizes: false, type: 'numeric'} : undefined}
        total={total}
        cells={cells}
        rows={rows}
        data={data} 
        take={take}
        skip={skip}
        defaultTake={10}
        defaultSkip={0}
        onPageChange={onPageChange}
        {...restProps}
        autoProcessData={false}
        sortable={true}
        >
            {columns.map((columnProps: GridColumnProps, index: number) => (
                <Column key={`${columnProps.field}_${index}`} {...columnProps} locked={columnProps.locked ? true : false} />
            ))}
        </Grid>
    );
};

export default DataGrid;

