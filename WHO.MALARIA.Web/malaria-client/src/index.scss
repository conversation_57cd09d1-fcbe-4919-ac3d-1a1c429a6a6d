@use "mixins.scss" as *;

/*font-family: 'Noto Sans', sans-serif;*/
@import url("https://fonts.googleapis.com/css2?family=Noto+Sans:wght@400;700&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Material+Icons");

/****************** BUTTON VARIABLES START ******************/

$app_btn_primary_bg: $primary_color;
$app_btn_primary_text_color: $white_color;
$app_btn_primary_hover_bg: $orange_color;

$app_btn_secondary_bg: $transparent_color;
$app_btn_secondary_text_color: $primary_color;
$app_btn_secondary_hover_bg: $grey_color;

/****************** BUTTON VARIABLES END ******************/

body {
  margin: 0;
  font-family: $font-stack;
  font-size: $font_size_14;
  color: $body_text_color;
  background-color: $white_color;
}

html,
body {
  height: auto;
  background-color: $white_color;
}

.hide {
  display: none;
}

.show {
  display: block;
}

.height100 {
  height: 100vh;
}

.padding-left-25 {
  padding-left: 25px !important;
}

#root .MuiBackdrop-root {
  background-color: #212529;
  opacity: 0.8 !important;
}

/* Global CSS*/
.app-main {
  min-height: calc(100vh - 65px);
}

.flex-container {
  height: 100%;
  padding: 0;
  margin: 0;
  display: flex;
  justify-content: center;
}

.form-control.inputfocus {
  margin: 0px;

  .MuiOutlinedInput-adornedEnd {
    padding-right: 0px;
  }

  .expand-icon {
    position: absolute;
    right: 0px;
    pointer-events: none;
  }
}

.inputfocus
  .MuiOutlinedInput-root.Mui-focused
  .MuiOutlinedInput-notchedOutline {
  border-color: #35add8;
}

.inputfocus .MuiOutlinedInput-root.Mui-error .MuiOutlinedInput-notchedOutline {
  border-color: #dd1f1f !important;
  border: 1px solid;
}

.Mui-error {
  color: #dd1f1f !important;
}

.inputfocus {
  .MuiFormLabel-root.Mui-focused {
    color: #35add8;
  }

  .Mui-error {
    color: #dd1f1f !important;
  }

  .MuiFormLabel-root.Mui-error {
    color: #dd1f1f !important;

    span {
      color: #dd1f1f !important;
    }
  }

  .MuiAutocomplete-inputRoot {
    button {
      width: 28px;
      height: 28px;
    }
  }
}

.blue-modal-text {
  color: #35add8;
  font-size: 18px;
  font-weight: 700;
}

.inputfocus .MuiRadio-colorPrimary.Mui-checked,
.inputfocus label .MuiRadio-root,
.inputfocus .MuiRadio-colorPrimary {
  color: #35add8 !important;
}

.inputfocus label .MuiRadio-root svg {
  color: #35add8 !important;
}

.col-radiobox-control {
  .radiobox-control {
    span {
      color: #686868;
    }
  }

  > span {
    font-size: 14px;
    font-family: "Noto Sans", sans-serif;
  }

  .radiobox-control.Mui-checked {
    span {
      color: #35add8 !important;
    }
  }

  .radiobox-control.Mui-disabled {
    span {
      color: rgba(0, 0, 0, 0.26);
    }
  }
}

.col-checkbox-control {
  .checkbox-control {
    span {
      color: #686868;
    }
  }

  > span {
    font-size: 14px;
  }

  .checkbox-control.Mui-checked {
    span {
      color: #899caa !important;
    }
  }

  .checkbox-control.Mui-disabled {
    span {
      color: rgba(0, 0, 0, 0.26) !important;
    }
  }
}

.blue-background {
  background-image: url(./images/EllipsisIcon.svg),
    linear-gradient(94.98deg, #0b6ca5 3.93%, #35add8 100.87%);
  background-repeat: no-repeat;
  background-position: right bottom;
  background-color: linear-gradient(94.98deg, #0b6ca5 3.93%, #35add8 100.87%);
  color: #fff;
  padding: 20px 20px;
  border-radius: 20px;
}

.grey-background {
  background-color: #ececec;
  color: #383838;
  padding: 20px 20px;
  border-radius: 10px;
}

.grey-background .person-icon {
  color: #35add8;
}

.app-list-group .list-group .list-group-item {
  .col-checkbox-control {
    .checkbox-control {
      padding: 4px;
    }
  }
}

.MuiSelect-select:focus {
  background-color: $white_color !important;
}

.MuiPickersToolbar-toolbar,
.MuiPickersDay-daySelected {
  background-color: #35add8 !important;
}

.capitalize-text {
  text-transform: lowercase !important;

  .capitalize-text:first-line {
    text-transform: capitalize !important;
  }

  .capitalize-text:first-letter {
    text-transform: capitalize !important;
  }
}

small.fw-lighter {
  font-style: italic;
  color: #909090;
  line-height: 14px;
}

.questions-table {
  thead th {
    min-width: 70px !important;
  }

  tbody tr td {
    min-width: 70px !important;
  }
}

.MuiStepper-horizontal {
  justify-content: center;

  .MuiStep-horizontal {
    cursor: pointer;

    .MuiStepLabel-label.MuiStepLabel-completed {
      color: rgba(0, 0, 0, 0.54);
    }
  }
}

.h-300 {
  min-height: 300px;
}

.h-400 {
  min-height: 400px;
}

.h-500 {
  min-height: 500px;
}

.width-100 {
  min-width: 100px !important;
  width: 100px !important;
}

.width-250 {
  width: 250px;
  min-width: 250px;
}

.app-logo-text {
  display: inline-block;
  width: 250px;
}

.app-dashboard {
  min-height: 425px;
  border-bottom: 1px solid #e8e8e8;
  background: #f4faff;
  padding: 15px 20px;
  border-radius: 20px;
  border: 1px solid #e5f2fc;
}

.grey-circle {
  min-width: 150px;
  min-height: 150px;
  background-color: #c4c4c4;
  border-radius: 50%;
}

.table > :not(:first-child) {
  border-top: 0px;
}

.h-45 {
  min-height: 45px;
}

.link-text {
  color: #196aaa;
}

.landing-table-center {
  margin: 0 auto !important;
}

.custom-table {
  table {
    border: 1px solid rgba(0, 0, 0, 0.12);
    margin-bottom: 20px;

    thead {
      th {
        padding: 8px 15px;
        font-weight: 700;
        font-size: 13px;
      }
    }

    tr {
      td {
        border-color: rgba(0, 0, 0, 0.12);
        vertical-align: top;
        padding: 8px 15px;

        &:last-child {
          width: 70%;
        }

        &:first-child {
          width: 10%;
        }
      }
    }
  }

  .grey-table {
    thead {
      th {
        background-color: #ececec;

        &:first-child {
          width: 10%;
        }

        &:last-child {
          width: 70%;
        }
      }
    }
  }

  .table-grey {
    thead {
      th {
        background-color: #ececec;
      }
    }

    .table-highlight {
      background-color: #f5f5f5;
    }

    tr {
      td {
        &:last-child {
          width: auto;
        }

        &:first-child {
          width: auto;
        }
      }
    }
  }

  .table-blue {
    .grey-row {
      th {
        background-color: #ececec;
        color: rgba(0, 0, 0, 0.87);
      }
    }

    thead {
      th {
        background-color: #4f81bc;
        color: #fff;
      }
    }

    .table-highlight {
      background-color: #dae1f3;
    }
  }

  .table-green {
    thead {
      th {
        background-color: #339933;
        color: #fff;
      }
    }

    .table-highlight {
      background-color: #e2efdb;
    }
  }

  .table-purple {
    thead {
      th {
        background-color: #7030a0;
        color: #fff;
      }
    }

    .table-highlight {
      background-color: #dbc2ec;
    }
  }

  .table-red {
    thead {
      th {
        background-color: #bf0000;
        color: #fff;
      }
    }

    .table-highlight {
      background-color: #ffd8d9;
    }
  }
}

.survey-table {
  margin-top: 10px;

  thead {
    th {
      background-color: #ececec;
      padding: 8px 20px;
      font-weight: 700;
      font-size: 13px;
      color: #646464 !important;

      .col-checkbox-control .checkbox-control {
        padding: 0px;
      }
    }
  }

  tr {
    td {
      border-color: rgba(0, 0, 0, 0.12);
      vertical-align: top;
      padding: 15px 10px;

      &:last-child {
        width: 20%;
      }

      &:first-child {
        width: 5%;

        label {
          margin-right: 0px;
        }
      }
    }

    .notes-text {
      label {
        white-space: pre-line;
      }
    }
  }
}

.card-blue-box {
  border-bottom: 1px solid #e8e8e8;
  background: #f4faff;
  padding: 15px 20px;
  border-radius: 20px;
  border: 1px solid #e5f2fc;
  margin: 20px 0px;

  .document-title-section {
    margin-bottom: 10px;
  }

  .summary p.collapse:not(.show) {
    min-height: 80px;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 4;
    -webkit-box-orient: vertical;
    margin-bottom: 0px;
  }

  .summary p.collapsing {
    min-height: 80px;
  }

  .summary .summary-btn {
    text-transform: uppercase;
    font-weight: bold;
  }

  a {
    color: #196aaa;
  }
}

/* Button CSS*/
.button-action-section {
  .btn + .btn {
    margin-left: 10px !important;
  }

  .btn + .register-link {
    margin-left: 15px !important;
    border-left: 1px solid #ccdbe8;
    padding: 7px 12px;
  }
}

.app-btn-full {
  width: 100%;
}

.app-btn-primary {
  background-color: $app_btn_primary_bg !important;
  color: $app_btn_primary_text_color !important;
  text-transform: uppercase;
  font-family: $font-stack !important;
  font-style: normal;
  font-weight: bold;
  font-size: 12px;
  line-height: 16px;
  align-items: center;
  border-radius: 4px;
  padding: 10px 20px;
  cursor: pointer;
  min-width: 100px;
}

.app-btn-primary:hover {
  color: $white_color !important;
  background-color: $app_btn_primary_hover_bg !important;
}

.app-btn-secondary {
  background-color: $app_btn_secondary_bg !important;
  color: $primary_color !important;
  border: 1px solid #196aaa !important;
  text-transform: uppercase;
  font-family: "Noto Sans", sans-serif;
  font-style: normal;
  font-weight: bold;
  font-size: 12px;
  line-height: 16px;
  align-items: center;
  border-radius: 4px;
  padding: 5px 12px;
  cursor: pointer;
  min-width: 100px;
}

.app-btn-secondary:hover {
  color: #196aaa !important;
  background-color: $app_btn_secondary_hover_bg !important;
}

.app-btn-secondary-grey {
  border: 1px solid #ececec !important;
  color: #ececec !important;
}

.app-btn-orange {
  background-color: #e57041 !important;
  color: $white_color !important;
  text-transform: uppercase;
  font-family: $font-stack !important;
  font-style: normal;
  font-weight: bold;
  font-size: 12px;
  line-height: 16px;
  align-items: center;
  border-radius: 4px;
  padding: 10px 20px;
  cursor: pointer;
  min-width: 100px;
}

.app-btn-orange:hover {
  background-color: #e57041 !important;
  color: $white_color !important;
}

.btn-primary-color {
  color: $primary_color !important;
  font-weight: 700 !important;
  text-transform: capitalize !important;
  padding: 5px 10px !important;
  border-right: 0px !important;
  margin: 0px 10px !important;

  &:hover {
    background-color: transparent !important;
  }
}

.btn-green {
  background-color: #29b05f !important;
  padding: 4px 15px;
  color: #fff !important;
  font-size: 12px;
  line-height: 16px !important;
  border-radius: 4px;
}

.btn-grey {
  background-color: #899caa !important;
  padding: 4px 10px;
  color: #fff !important;
  font-size: 12px;
  line-height: 16px !important;
  border-radius: 4px;
}

.btn-orange {
  background-color: #db7e59 !important;
  padding: 4px 10px;
  color: #fff !important;
  font-size: 12px;
  line-height: 16px !important;
  border-radius: 4px;
}

.MuiTooltip-popper {
  .MuiTooltip-tooltip {
    font-size: 12px;
  }
}

.icon-button-primary {
  color: $primary_color !important;
}

.cursor-pointer {
  cursor: pointer;
}

/* BreadCrumb CSS*/
.breadcrumb-wrapper {
  padding-right: 11rem;
  word-wrap: break-word;
  word-break: break-word;

  ol {
    li {
      .active-breadcrumb {
        font-size: 16px;
        font-weight: 700;
        color: #646464;
      }

      a {
        color: #686868;
        font-size: 14px;

        &:hover {
          color: #db7e59;
        }
      }
    }
  }
}

/* Sections Global CSS*/
.page {
  &-grid-section {
    height: calc(100vh - 65px);
    background-color: $grid_bg_color;
  }

  &-full-section {
    padding: 15px 15px;
    min-height: calc(100vh - 65px);
    height: 100%;
  }

  &-column-section {
    padding: 15px 0px;
  }

  &-sticky-head-section {
    position: sticky;
    top: 66px;
    background-color: #fff;
    z-index: 999;
  }

  &-full-assess-section {
    position: fixed;
    top: 0px;
    height: 100%;
    z-index: 1050;
    background-color: #f3f6f8;
    width: 100%;
    padding: 15px 15px 15px 30px;
    overflow-y: scroll;
  }

  &-response-section {
    padding: 5px 20px;
    margin-bottom: 5%;
  }

  &-center-section {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px 0px;
    min-height: 550px;

    .w-50 {
      width: 50%;
    }

    .button-action-section {
      display: flex;
      justify-content: center;
      padding-top: 50px;
    }
  }

  &-report-section {
    .k-grid-wrapper {
      .k-grid-header {
        .grid-icon {
          display: none;
        }
      }
    }
  }
}

.page-report-section {
  .k-grid-wrapper {
    .k-grid-header .k-header {
      cursor: default;
    }

    table {
      tr:hover {
        cursor: default;
      }
    }
  }

  .k-grid-content {
    table {
      tr {
        td:last-child {
          text-align: left !important;
        }
      }
    }
  }

  .k-grid-wrapper + .k-grid-wrapper {
    margin-top: 25px;
  }
}

/* Analytical output page graph CSS*/
.page-graph-section {
  .graph-wrapper {
    > div:first-child {
      display: none;
    }
  }
}

/* DR Indicator Response CSS*/
.page-response-section {
  position: relative;

  > .col-checkbox-control {
    display: flex;
    justify-content: end;
  }

  .response-wrapper {
    padding: 20px 15px;
    background-color: #fff;
  }

  .MuiStepper-horizontal {
    background-color: transparent;
    padding: 5px 24px;
    width: 50%;
    position: absolute;
    left: 0%;
    top: 10px;
    display: inline-flex;

    .MuiStep-horizontal {
      cursor: pointer;

      span {
        cursor: pointer;
      }
    }

    .MuiStepIcon-root.MuiStepIcon-completed {
      color: #008dc3;
    }

    .MuiStepIcon-root.MuiStepIcon-active {
      color: #008dc3;
    }
  }

  .response-content {
    padding: 5px 0px;
  }

  .radio-wrapper {
    margin-bottom: 20px;

    .col-radiobox-control {
      width: 50%;
      margin-right: 0px !important;
    }
  }

  .inputfocus {
    input::placeholder {
      font-style: italic;
      font-size: 14px;
    }

    textarea::placeholder {
      font-style: italic !important;
      font-size: 14px;
    }
  }

  .inputfocus.lp-text {
    > div {
      padding-top: 3.5rem;
    }
  }

  .response-action-wrapper {
    .button-action-section {
      position: fixed;
      bottom: 0px;
      left: 0px;
      right: 0px;
      background-color: #f3f6f8;
      z-index: 99;
    }
  }

  .response-assess-wrapper {
    display: flex;
    justify-content: end;
  }

  .response-status-wrapper {
    position: absolute;
    right: 5%;
    transform: translate(1rem, -3.8rem);
  }
}

/* Checkbox List CSS*/
.register-type-wrapper {
  .checkbox-list {
    border-top: none;
    padding-top: 0px;
    border: 1px solid rgba(0, 0, 0, 0.12);

    ul {
      padding: 0px;

      .col-checkbox-control {
        border-top: 1px solid rgba(0, 0, 0, 0.12);

        &:first-child {
          border-top: none;
        }
      }
    }
  }
}

.checkbox-list {
  border-top: 1px solid #eeeeee;
  padding-top: 15px;

  &:first-child {
    border-top: none;
  }

  h6 {
    font-size: 14px;
    padding: 0px 15px;
    margin-bottom: 0px;
  }

  ul {
    .col-checkbox-control {
      text-transform: capitalize;

      > div {
        span.checkbox-control {
          padding: 4px 9px;
        }
      }
    }
  }

  .col-checkbox-control {
    .MuiListItemIcon-root {
      min-width: 20px;
    }
  }
}

.checkbox-list-horizontal {
  ul {
    display: flex;
    flex-direction: row;
    padding: 0px;
  }
}

.checkbox-list-sidebar {
  .radiobox-list:first-child {
    padding-top: 0px;
  }

  .radiobox-list {
    padding-top: 15px;

    .col-radiobox-control {
      text-transform: capitalize;
      padding: 0px 10px;
      display: flex;
    }
  }
}

.drawerContent {
  padding: 20px 20px;
  white-space: normal;

  ul {
    padding-left: 0px;

    li {
      list-style: none;
      color: #686868;
      font-weight: 400;
      font-size: 13px;

      br {
        content: "";
        display: block;
        margin: 15px 0px;
      }

      h5 {
        color: #686868;
        font-weight: 700;
        font-size: 14px;
        display: block;
        position: relative;
        line-height: 30px;

        .info-icon {
          position: absolute;
          right: 0px;
          width: 30px;
          height: 30px;
          z-index: 99;
        }
      }

      h6 {
        color: #686868;
        font-weight: 700;
        font-size: 14px;
        line-height: 18px;
        display: block;
        position: relative;
        text-decoration: underline;
        margin: 0.5rem 0px;
      }

      b {
        display: block;
        margin-bottom: 1rem;
      }
    }
  }

  .drawer-list {
    margin-bottom: 25px;
  }

  .drawer-title {
    font-weight: 700;
    font-size: 30px;
  }

  span.icon {
    display: flex;

    &:before {
      font-family: "Material Icons";
      width: 20px;
      height: 20px;
      display: inline-flex;
      font-size: 20px;
      color: #5d6d73;
      align-items: center;
      margin-right: 10px;
    }
  }

  .icon-librarybooks:before {
    content: "\e02f";
  }

  .icon-person:before {
    content: "\e7fb";
  }

  .icon-storage:before {
    content: "\e1db";
  }
}

.icon-done-tick {
  color: #1fbb54 !important;
}

.k-grid-header .k-header.k-sorted {
  span.k-head-text:after {
    font-size: 17px;
    line-height: 16px;
    font-weight: 700;
    margin-left: 5px;
  }

  &[aria-sort="ascending"] {
    span.k-head-text:after {
      content: "\2193";
    }
  }

  &[aria-sort="descending"] {
    span.k-head-text:after {
      content: "\2191";
    }
  }
}

/*Tabs CSS*/
.app-tab-wrapper {
  border: 1px solid #ececec;

  div {
    div {
      div {
        .p-2 {
          div {
            div {
              .k-grid-action-wrapper {
                .k-grid-header {
                  .k-grid-header-wrap {
                    span {
                      white-space: normal;
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }

  .tab-panel {
    padding: 15px 15px;
  }

  .tabs-wrapper {
    .app-tabs {
      .app-close-tab {
        > span {
          align-items: center !important;
          white-space: pre-line;
          flex-direction: row-reverse;
          margin-left: -5px;
          justify-content: left;

          button {
            margin-bottom: 0px;
            margin-left: 10px;
            padding: 5px 5px;
            position: absolute;
            z-index: 99;
            right: 0;

            svg {
              font-size: 1.2rem;
            }
          }
        }
      }
    }
  }

  .tabs-wrapper {
    .app-tabs {
      min-height: 40px;

      .app-tab {
        background-color: #ececec;
        color: #909090;
        border: 1px solid #ffffff;
        text-transform: capitalize;
        font-weight: 700;
        min-height: 40px;
        position: relative;

        &:hover {
          background-color: $orange_color;
          color: $white_color;
        }

        > span {
          align-items: start;
          white-space: pre-line;

          span {
            text-align: left;
          }
        }
      }

      .app-tab.Mui-selected {
        background-color: #d6f4ff;
        color: #008dc3;
        font-weight: 700;
      }

      .MuiTabs-indicator {
        background-color: #008dc3;
        height: 0px;
      }
    }

    .app-tabs-scrollable {
      .MuiTabs-scrollButtons {
        svg {
          color: #008dc3;
        }
      }
    }
  }
}

.app-centered-tab-wrapper {
  border: none;

  .tabs-wrapper .app-tabs .MuiTabs-flexContainer {
    justify-content: center !important;
  }
}

.tab-wrapper-center {
  .tabs-wrapper .app-tabs .app-tab > span {
    flex-direction: row-reverse;
    align-items: center;
    justify-content: left;
  }
}

.app-add-tab-wrapper {
  .tabs-wrapper {
    .app-tabs {
      width: 95%;
    }
  }

  .add-btn {
    position: absolute;
    top: 0px;
    right: 0px;
  }
}

/* App Table CSS*/
.app-table {
  border: 1px solid #ececec;
  margin-bottom: 20px;
  width: 100%;

  thead {
    th {
      background-color: #f3f6f8;
      padding: 6px 10px;
      font-size: 12px;
      font-weight: 700;
      border-top: 1px solid #ececec;
      max-width: 250px;
      border-bottom-width: inherit;

      span {
        display: inline-block;
        padding: 0px;
      }
    }

    th:first-child {
      position: sticky;
      left: -1px;
      min-width: 250px;
      max-width: 350px;
      background-color: #f3f6f8;
      z-index: 99;
      border-left: 1px solid #ececec;
    }
  }

  tbody {
    tr {
      td {
        border-top: 1px solid #ececec;
        padding: 3px 10px;
        min-width: 250px;
        vertical-align: baseline;
        font-size: 14px;

        .app-error {
          input {
            padding: 10px 5px;
          }

          select {
            padding: 10px 15px;
          }
        }

        .inputfocus {
          input {
            padding: 10px 5px;
          }

          textarea::placeholder,
          input::placeholder {
            font-style: italic !important;
          }

          select {
            padding: 10px 15px;
          }
        }

        .inputfocus {
          .Mui-disabled {
            input {
              cursor: not-allowed;
            }

            fieldset {
              border-color: rgba(0, 0, 0, 0.06) !important;
              cursor: not-allowed;
            }
          }
        }
      }

      td:first-child {
        position: sticky;
        left: -1px;
        min-width: 250px;
        max-width: 350px;
        background-color: #fff;
        z-index: 99;
        border-left: 1px solid #ececec;
      }
    }

    tr.app-error {
      td {
        vertical-align: top;
      }
    }
  }

  .vertical-text {
    bottom: 0;
    font-weight: bold;
    height: 20px;
    margin: 0% 50%;
    padding: 0;
    text-align: left;
    -webkit-transform: rotate(-90deg);
    transform: rotate(-90deg);
    -webkit-transform-origin: left;
    transform-origin: left;
    white-space: nowrap;
    display: flex;
    justify-content: center;
    font-size: 14px;
  }

  .horizontal-text {
    font-weight: bold;
    font-size: 14px;
  }

  tfoot {
    background: rgba(33, 33, 33, 0.08);

    td:first-child {
      min-width: 250px;
      max-width: 325px;
      z-index: 99;
      display: flex;
      align-items: center;
    }

    td {
      padding: 3px 10px;

      > span {
        display: inline-block;
        padding: 0px;
      }
    }
  }
}

.app-table.minWidth tbody tr td {
  min-width: 230px;
}

.table-responsive {
  + .table-responsive {
    margin-top: 20px;
  }
}

.app-table-sticky-scroll {
  thead,
  tbody tr {
    display: table;
    width: 100%;
    table-layout: fixed;
    /* even columns width , fix width of table too*/
  }

  tbody {
    display: block;
    height: 400px;
    /* Just for the demo          */
    overflow-y: scroll;
    /* Trigger vertical scroll    */
    overflow-x: hidden;

    /* Hide the horizontal scroll */
    tr:last-child {
      position: sticky;
      bottom: 0px;
      background-color: #fff;
      text-align: center;
      border-top: 1px solid #ececec;
    }
  }
}

.app-table-scroll {
  thead,
  tbody tr {
    display: table;
    width: 100%;
    table-layout: fixed;
    /* even columns width , fix width of table too*/
  }

  tbody {
    display: block;
    height: 400px;
    /* Just for the demo          */
    overflow-y: scroll;
    /* Trigger vertical scroll    */
    overflow-x: hidden;
    /* Hide the horizontal scroll */
  }
}

.app-table-border {
  tbody {
    tr {
      td {
        border: 1px solid #ececec;
      }
    }
  }
}

.app-table-vertical {
  tbody tr td:first-child {
    position: relative;
    left: 0px;
  }

  tbody tr td:first-child[rowspan] {
    position: relative;
    border-right: 1px solid #ececec;
    left: 0px;
    width: 50px;
    min-width: 50px;
    max-width: 50px;
    vertical-align: middle;
  }

  tbody tr td.horizontal-text:first-child[rowspan] {
    width: 140px;
    min-width: 140px;
    vertical-align: middle;
  }

  thead th:first-child {
    width: 20px;
    min-width: 20px;
    max-width: 20px;
  }

  tfoot {
    td:first-child {
      width: 20px;
      min-width: 20px;
      max-width: 20px;
    }

    td[colspan] {
      text-align: left;
    }
  }
}

.questions-table thead tr th:first-child {
  width: 75% !important;
}

.td-percentage {
  border: 1px solid #ececec;
  vertical-align: middle !important;
}

.graph-wrapper {
  .inputfocus {
    input {
      padding: 10px 5px;
    }
  }

  .k-widget {
    svg {
      text {
        font-family: Roboto, "Helvetica Neue", sans-serif !important;
        font-size: 11px !important;
      }
    }
  }
}

.section-graph-wrapper {
  .app-table {
    thead th:first-child {
      position: relative;
      /*width: 120px;
            min-width: 120px;*/
    }

    thead th .inputfocus {
      padding: 0;
    }

    tbody tr td:first-child {
      position: relative;
      width: 160px;
      min-width: 160px;
    }

    tbody tr td .form-control .MuiInputLabel-formControl {
      font-size: 14px;
    }
  }
}

/**Modal dialog */
.ml-auto {
  margin-left: auto !important;
}

legend {
  float: none !important;
  width: 100%;
  padding: 0;
  margin-bottom: 0.5rem;
  font-size: calc(1.275rem + 0.3vw);
  line-height: inherit;
}

.app-modal-dialog .app-modal-body .button-action-section {
  margin-top: 20px;
  justify-content: center;
}

.app-modal-open {
  overflow: hidden;
}

/* The Modal (background) */
.app-modal {
  display: none;
  /* Hidden by default */
  position: fixed;
  /* Stay in place */
  /*z-index: 1;*/
  /* Sit on top */
  left: 0;
  top: 0;
  width: 100%;
  /* Full width */
  height: 100%;
  /* Full height */
  overflow: auto;
  /* Enable scroll if needed */
  background-color: rgba(0, 0, 0, 0.2);
  /* Fallback color */
}

/* The Modal (background) */
.app-modal {
  display: block;
  /* Hidden by default */
  position: fixed;
  /* Stay in place */
  /*z-index: 100;*/
  /* Sit on top */
  left: 0;
  top: 0;
  width: 100%;
  /* Full width */
  height: 100%;
  /* Full height */
  overflow: auto;
  /* Enable scroll if needed */
  opacity: 0;
}

.app-modal-dialog {
  min-height: calc(100vh - 60px);
  display: flex;
  flex-direction: column;
  justify-content: center;
}

@media (max-width: 768px) {
  .app-modal-dialog {
    min-height: calc(100vh - 20px);
  }
}

.app-modal-bg .app-modal-body {
  color: #aee9ff;
}

/* Modal Content/Box */
.app-modal-body {
  padding: 20px 30px;
  position: relative;
  color: #686868;
  font-size: 14px;
}

/* The Close Button */
.app-modal-close {
  color: #aaa;
  float: right;
  font-size: 28px;
  font-weight: bold;
}

.app-modal-close:hover,
.app-modal-close:focus {
  color: black;
  text-decoration: none;
  cursor: pointer;
}

@media (min-width: 576px) {
  .app-modal-md .app-modal-dialog {
    width: 50%;
    margin: 0% auto !important;
  }

  .app-modal-right .app-modal-dialog {
    max-width: 72%;
    margin: 0% auto !important;
  }
}

@media (min-width: 992px) {
  .app-modal-xs .app-modal-dialog {
    width: 25%;
    margin: 0% auto !important;
  }

  .app-modal-sm .app-modal-dialog {
    width: 40%;
    margin: 0% auto !important;
  }

  .app-modal-lg .app-modal-dialog {
    width: 75%;
    margin: 0% auto !important;
  }
}

.app-modal-dialog {
  position: relative;
  margin: 0.5rem;
}

.app-modal-bg .app-modal-header {
  padding: 1rem 1rem;
}

.app-modal-bg .app-modal-content {
  content: "";
  background-image: url(images/ModalBackground.png);
  background-repeat: repeat;
  border-radius: 20px;
}

.app-modal-content {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  width: 100%;
  pointer-events: auto;
  background-color: #fff;
  background-clip: padding-box;
  outline: 0;
  border-radius: 20px;
}

.app-modal-header {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: center;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  padding: 1rem 1rem 1rem 25px;
  border-top-left-radius: 0.3rem;
  border-top-right-radius: 0.3rem;
}

.app-modal-header .app-modal-header-text {
  margin: 5px;
  font-weight: 700;
  text-transform: uppercase;
}

.app-modal-open {
  overflow: hidden;
}

.app-modal.app-modal-show {
  overflow-x: hidden;
  overflow-y: auto;
  opacity: 1;
  z-index: 1072;
}

.full-screen .app-modal-dialog {
  max-width: 100%;
  width: 100% !important;
  margin: 0% auto !important;
  height: 100vh;
}

.full-screen .app-modal-content {
  height: 100vh;
}

.app-modal-body .form-wrapper {
  min-height: auto;
}

.app-modal-open .header {
  position: static;
}

.app-modal-header .modal-hd-btn {
  color: var(--font-color);
  text-shadow: 0 1px 0 #fff;
  opacity: 0.5;
  padding: 8px 10px;
  font-size: 18px !important;
  background-color: transparent;
  border: 0;
  line-height: 18px;
}

.app-modal-header .modal-hd-btn:hover {
  border-radius: 50px;
  background-color: rgba(0, 0, 0, 0.04);
  color: #000;
  text-shadow: 0 1px 0 #fff;
  opacity: 0.5 !important;
}

.app-modal-dialog .app-modal-body .button-action-section {
  margin-top: 20px;
}

.app-modal-dialog .app-modal-body .button-action-section .button-group {
  margin-top: 17px;
}

.app-modal-dialog .modal-content-head {
  font-size: 18px;
}

.loader {
  position: fixed;
  left: 0px;
  top: 0px;
  width: 100%;
  height: 100%;
  z-index: 9999 !important;
  color: #196aaa !important;
}

.bg-primary {
  background-color: #196aaa !important;
}

.app-modal-bg .close-modal {
  color: #c5efff !important;
}

.app-modal .close-modal {
  color: #727272;
}

.app-modal-full {
  .app-modal-dialog {
    max-width: 100%;
    width: 100% !important;
    margin: 0% auto !important;
    height: 100vh;
    border-radius: 4px;
  }

  .app-modal-header {
    .MuiStepper-horizontal {
      min-width: 40%;
    }
  }

  .app-modal-content {
    height: 100vh;
    border-radius: 4px;
  }
}

.btn-modal-primary {
  background-color: #aac7d2 !important;
  color: #3a4549 !important;
  font-size: 12px !important;
  font-weight: 700 !important;
  padding: 8px 15px !important;
  font-family: "Noto Sans", sans-serif;
}

.bd-highlight {
  min-width: 70% !important;
  text-align: center;
  color: #383838 !important;
}

.line-highlight {
  padding: 15px 15px;
}

.swipeable-drawer {
  .MuiDrawer-paperAnchorLeft {
    width: 25%;
  }
}

/*Document Title CSS*/
.document-title-section {
  padding: 10px 0px;
  align-items: center;

  .heading-title {
    color: #646464;
    font-size: 16px;
    line-height: 20px;
    margin-bottom: 0px;
    font-weight: 700;
    text-transform: uppercase;
  }
}

/* Header navigation CSS*/
.header-section {
  .navbar-nav {
    .nav-link {
      padding: 2px 12px;
      text-transform: uppercase;
      position: relative;

      svg.bi-globe {
        width: 20px;
        height: 20px;
      }
    }

    .nav-link.active,
    .nav-link.show {
      font-weight: 700;
      color: #fff;
    }

    .nav-link.active:after,
    .nav-link.show:before {
      content: "";
      border-bottom: 2px solid #fff;
      width: 25px;
      height: 2px;
      position: absolute;
      display: block;
      right: 15px;
      bottom: 0px;
    }
  }

  .language-selection {
    .nav-link {
      top: 5px;
    }
  }

  .country-selection {
    .country-selection-dropdown {
      position: absolute;
      bottom: 35px;

      .dropdown-menu {
        .dropdown-item {
          font-size: 14px !important;
          color: #333 !important;
        }

        .dropdown-item:focus,
        .dropdown-item:hover {
          color: #1e2125 !important;
          background-color: #e9ecef !important;
        }
      }

      select {
        background-color: #196aaa !important;
        color: rgba(255, 255, 255, 0.55) !important;
        border: none;
        border-radius: 0px;
      }

      fieldset {
        border: none;
      }

      .form-control.inputfocus .expand-icon {
        color: rgba(255, 255, 255, 0.55);
      }
    }

    .country-role {
      top: 10px;
      color: rgba(255, 255, 255, 0.55);
    }
  }
}

/* Multiselect CSS */
.multi-select .chip-wrap {
  background-color: #c9f1ff;
  color: #383838;
  margin: 2px;

  .MuiChip-deleteIcon {
    color: #8bafbb;
  }
}

/*Carousel CSS*/
#app-carousel {
  min-height: 425px;
  border-bottom: 1px solid #e8e8e8;
  background: #f4faff;
  padding: 15px 20px;
  border-radius: 20px;
  border: 1px solid #e5f2fc;

  .carousel {
    .control-dots {
      .dot {
        width: 7px;
        height: 7px;
        background-color: #757575;
        box-shadow: none;
        vertical-align: middle;

        &.selected {
          width: 12px;
          height: 12px;
          background-color: #db7e59;
        }
      }
    }
  }
}

.carousel-inner {
  position: relative;
  width: 100%;
  overflow: hidden;
}

.carousel-caption {
  position: relative;
  left: 0px;
  right: 0px;
  color: #000;
  text-align: left;
  padding-bottom: 0px;
}

.carousellist li::marker {
  list-style: none;
}

.carousel-item .img-fluid {
  border-radius: 15px;
}

.carousellist li {
  position: relative;
  padding: 20px 0px;
  display: flex;
  justify-content: normal;
  align-items: center;
}

.carousel-list-content {
  padding-left: 20px;

  b {
    font-size: 16px;
    color: #383838;
  }
}

.carousel-list-icon {
  width: 60px;
  height: 60px;
}

.carousel-list-bg-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
}

.carousel-list-icon::before {
  content: "";
  position: absolute;
  background-repeat: no-repeat;
  background-position: 0px 0px;
  width: 60px;
  height: 60px;
  top: 20px;
  left: 0px;
  cursor: pointer;
}

.assessment-icon-1::before {
  background-image: url(./images/AssessmentInitiated.svg);
}

.assessment-icon-2::before {
  background-image: url(./images/AssessmentsInProgress.svg);
}

.assessment-icon-3::before {
  background-image: url(./images/CompletedOneAssessment.svg);
}

.assessment-icon-4::before {
  background-image: url(./images/CompletedMoreAssessments.svg);
}

.control-arrow:before {
  content: "";
  position: relative;
  background-repeat: no-repeat;
  background-position: 0px 0px;
  width: 25px;
  height: 35px;
  cursor: pointer;
  z-index: 99;
  margin: 0px !important;
  border: 0px !important;
}

.carousel-control-next {
  right: -5px;
}

.carousel-control-prev {
  left: -5px;
}

.carousel.carousel-slider .control-arrow:hover {
  background: transparent;
}

.carousel.carousel-slider .control-arrow {
  opacity: 1;
}

.carousel .control-next.control-arrow {
  right: -15px;
}

.control-prev::before {
  background-image: url(./images/PrevArrow.png);
  background-size: auto;
}

.control-next::before {
  background-image: url(./images/NextArrow.png);
  background-size: auto;
}

#app-dashboard-carousel .carousel .control-next.control-arrow,
#app-dashboard-carousel .carousel .control-prev.control-arrow {
  height: 30px;
  top: 15px;
}

#app-dashboard-carousel {
  .carousel {
    .slide {
      .row {
        margin-left: 0px;
      }
    }
  }
}

/*Card CSS*/
.app-card-wrapper {
  .card {
    border: 1px solid #e1e1e1;
    @include border-radius(12px);
  }

  .card-title {
    font-size: 16px;
    font-weight: 700;
    color: #383838;
  }

  .card-text {
    color: #686868;
    margin-bottom: 0px;
  }
}

/* Kendo Grid CSS*/
.k-grid-action-wrapper {
  .k-grid-header {
    .k-filter-row {
      th:last-child {
        .k-filtercell {
          display: none;
        }
      }
    }
  }

  .k-grid-content {
    .k-grid-table {
      tr {
        td:last-child {
          text-align: right;
        }
      }

      .k-grid-norecords {
        td {
          text-align: center !important;
        }
      }
    }
  }
}

.app-tab-wrapper
  .k-grid-wrapper.k-grid-action-wrapper
  .k-grid-header
  .k-header {
  padding: 8px 10px;
}

.k-grid-wrapper {
  border: none;

  .k-grid-header {
    padding-right: 0px !important;

    table {
      width: 100% !important;
    }

    .k-header {
      text-transform: capitalize;
      background-color: #ececec;
      padding: 8px 10px;
      font-weight: 700;
      font-size: 12px;
      border-bottom: 0px !important;
      border: none;
      color: #686868;
      vertical-align: middle;

      &:last-child {
        .grid-icon-hide {
          display: none;
        }
      }

      .k-link {
        padding: 12px 24px;

        span {
          vertical-align: middle;
          white-space: normal;
        }
      }

      .grid-icon {
        margin-left: 10px;

        button {
          padding: 5px 5px;
          font-size: 20px;

          svg {
            font-size: 20px;
          }
        }
      }
    }
  }

  .k-grid-content {
    .k-grid-table {
      width: 100% !important;
    }
  }

  table tr:hover {
    background-color: transparent;
    cursor: pointer;
  }

  table tr td {
    padding: 5px 10px;
    color: #383838;
    white-space: nowrap;
    font-size: 13px;
  }

  .k-grid-content tr:last-child > td,
  .k-grid-content-locked tr:last-child > td {
    border-bottom-width: 1px;
  }

  .grid-icon-button {
    padding: 0px 0px !important;
    color: #c4c4c4;
    margin: 0px 5px;
  }

  .grid-icon-button-grey {
    padding: 0px 0px;
    margin: 0px 5px;
    color: #ececec;
  }

  .col-checkbox-control {
    margin-left: 0px;

    .checkbox-control {
      padding: 0px;
    }
  }

  .k-pager-wrap {
    border: none;
    padding: 2px 15px;
    display: flex;
    justify-content: center;

    .k-pager-info {
      flex: inherit;
      order: 9;
      display: inline-flex;
      margin: 0px 12px;
      font-size: 13px;
    }

    .k-icon {
      font-size: 20px;
      color: rgba(0, 0, 0, 0.54);

      &:hover {
        color: rgba(0, 0, 0, 0.54);
      }
    }
  }

  .k-filter-row {
    .k-textbox {
      padding: 5px 0px;
      border-color: rgba(0, 0, 0, 0.12);
      border-style: solid;
    }

    .k-datepicker .k-picker-wrap,
    .k-timepicker .k-picker-wrap,
    .k-datetimepicker .k-picker-wrap,
    .k-dateinput .k-dateinput-wrap {
      border-color: rgba(0, 0, 0, 0.12);
      color: #383838;
      font-weight: 400;
    }
  }

  .k-pager-numbers {
    .k-link.k-state-selected {
      color: $primary_color;
      font-weight: 700;
      background-color: #ececec;
    }

    .k-link {
      color: $primary_color;
      margin: 0px 2px;
      font-weight: 700;
      min-width: calc(1.4285714286em + 14px);
      height: calc(1.4285714286em + 14px);

      &:hover {
        background-color: #ececec;
      }
    }
  }

  .k-pager-sizes {
    .k-select {
      padding: 4px 4px;
    }
  }

  .k-dropdown .k-dropdown-wrap,
  .k-dropdowntree .k-dropdown-wrap {
    border: 1px solid #fff !important;
    padding: 2px 5px;
    border-radius: 8px;
  }

  .k-filter-row {
    td {
      padding: 0px 0px;
      border: 0px;
    }

    th {
      span,
      input {
        font-size: 13px;
      }

      .form-control {
        border-bottom: 1px solid #ddd;
        border-radius: 0px;
      }
    }

    .form-control {
      .MuiInput-underline:before,
      .MuiInput-underline:after {
        border-bottom: 0px !important;
      }

      select {
        padding: 7px 5px !important;
        cursor: pointer !important;
        font-size: 13px;
        width: 100%;
        min-width: 135px;
      }

      span {
        font-size: 13px;
      }

      button {
        width: 28px;
        height: 28px;
      }
    }

    .k-calendar-container {
      display: block !important;
      position: absolute;
      top: 221px !important;
      left: 489px !important;
    }
  }

  .k-filter-row th {
    padding: 2px 24px;
  }
}

.page-report-section {
  .k-grid-wrapper {
    .k-grid-header {
      .k-header {
        text-transform: inherit !important;
      }
    }
  }
}

.k-grid-wrapper .app-btn-secondary {
  padding: 5px 12px !important;
  line-height: 14px !important;
}

.k-grid-wrapper .k-grid-content {
  overflow-y: auto;
}

.k-grid-wrapper col.k-sorted,
.k-grid-wrapper th.k-sorted {
  background-color: transparent;
}

.k-desk-review {
  .k-grid-header .k-header:last-child {
    width: 150px;
  }

  .k-grid-content .k-grid-table tr td:last-child {
    width: 150px;
  }
}

.k-grid-summary {
  .k-grid-header .k-header:first-child .grid-icon {
    display: none;
  }

  .k-grid-header .k-header .grid-icon-hide button {
    display: none;
  }

  .k-grid-header {
    tr {
      th:last-child {
        width: 25%;
      }

      th:first-child {
        width: 25%;
      }

      th:nth-last-child(2) {
        width: 18%;
      }
    }
  }

  .k-grid-table {
    tr {
      td:last-child {
        width: 25%;
      }

      td:first-child {
        width: 25%;
      }

      td:nth-last-child(2) {
        width: 18%;
      }
    }
  }
}

.main-menu {
  position: relative;
  top: 10px;
}

.header-dropdown {
  .dropdown-menu {
    padding: 15px 15px;

    .dropdown-item {
      font-size: 14px !important;
      padding: 8px 16px;
      color: #333 !important;
      cursor: pointer;
      border-bottom: 1px solid #e0e0e0;
      text-transform: capitalize !important;

      .nav-link {
        color: #333 !important;
        padding: 0px;
        text-transform: capitalize !important;
      }
    }

    .dropdown-item:hover,
    .dropdown-item:focus {
      background-color: #e9ecef !important;
    }

    .dropdown-item:last-child {
      border-bottom: none;
    }
  }
}

/* Profile Section CSS*/
.app-profile-section {
  border-left: 1px solid #5d9acb;

  .media .media-body .user-name {
    font-size: 14px;
    font-weight: 500;
    text-transform: capitalize;
  }

  .nav-item .nav-link {
    padding: 6px 8px !important;

    .user-name {
      width: 100px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      display: inline-block;
    }
  }

  .dropdown-menu {
    padding: 15px 15px;

    .dropdown-item {
      font-size: 14px !important;
      padding: 8px 16px;
      color: #333 !important;
      cursor: pointer;
      border-bottom: 1px solid #e0e0e0;
      text-transform: capitalize !important;
    }

    .dropdown-item:hover,
    .dropdown-item:focus {
      background-color: #e9ecef !important;
    }

    .dropdown-item:last-child {
      border-bottom: none;
    }
  }
}

/* Application Dashboard  CSS*/
.dashboard-section .app-card-wrapper .card {
  border: 1px solid #cdcdcd;
  border-radius: 15px;
  min-height: 200px;
}

.dashboard-section .carousel [class*=" col-md"] {
  width: 100%;
}

/*Assessment CSS*/
.assessment-progress-wrapper .box-wrap .progress-custom-wrap {
  background-color: #c7ead3;
  height: 6px;
}

.assessment-progress-wrapper .box-wrap .progress-custom-wrap > div {
  background-color: #19cd56;
}

/* Background image 404 CSS*/
.section-background-image {
  background-image: url(images/BackgroundWithCircles.svg);
  background-position: center center;
  background-repeat: no-repeat;
  background-size: cover;
  background-color: #999;
  width: 100%;
  height: calc(100vh - 65px);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.section-bg-content {
  text-align: center;
  width: 300px;

  h1 {
    font-weight: bold;
    font-size: 96px;
    color: #35add8;
    position: relative;

    .lock-icon {
      position: relative;
      top: -5px;
      margin-left: 4px;
    }
  }

  h1.heading-bg {
    color: #d6f4ff;

    &::before {
      content: "";
      border-bottom: 8px solid #d6f4ff;
      border-radius: 10px;
      position: absolute;
      bottom: -10%;
      width: 20%;
      left: 17%;
    }
  }

  .content {
    font-size: 20px;
    color: #ffffff;
    margin-top: 40px;
    margin-bottom: 20px;
  }

  .app-btn-secondary {
    border: 1px solid #fff !important;
    border-radius: 5px;
    color: #fff !important;
    min-width: 125px;
  }
}

/*Login action CSS*/
.login-action-section {
  position: sticky;
  bottom: 0px;
  background-color: #fff;
  z-index: 9;
}

.line {
  overflow: hidden;
  text-align: center;
}

.line:before,
.line:after {
  background-color: #979797;
  content: "";
  display: inline-block;
  height: 1px;
  position: relative;
  vertical-align: middle;
  width: 47%;
}

.line:before {
  right: 0.5em;
  margin-left: -50%;
}

.line:after {
  left: 0.5em;
  margin-right: -50%;
}

.line-after {
  position: relative;
  overflow: hidden;
  display: block;

  &:after {
    content: "";
    position: absolute;
    width: 50%;
    top: 50%;
    margin-left: 10px;
    border-top: 1px solid #c9dfe7;
  }
}

/* Indicators CSS*/
.indicators-wrapper {
  position: relative;
  flex: 0.8;
  margin-left: 15px;
  padding: 0px 15px;

  .k-grid-wrapper .k-grid-content {
    overflow-y: auto;
    max-height: 450px;
    min-height: 300px;
    height: auto;
  }

  .button-action-section.button-bottom {
    position: absolute;
    bottom: -75px;
    left: 25%;
  }
}

.category-wrapper {
  min-height: 400px;

  .subcategories-wrapper {
    flex: 0.1 1;
    border-right: 1px solid #ddd;

    ul {
      list-style: none;
      display: flex;
      flex-direction: column;
      vertical-align: top;
      width: 200px;
      padding: 0px 15px 0px 15px;
      margin-bottom: 0px;

      li {
        margin-bottom: 10px;
        font-weight: 400;
        cursor: pointer;
        padding-right: 20px;
        max-width: 200px;

        span {
          padding: 15px 8px;
        }
      }
    }
  }

  .indicators-wrapper {
    flex: 0.9 1;
    width: 83%;
  }

  .app-tab-wrapper .tabs-wrapper .app-tabs .app-tab > span {
    text-transform: none;
  }
}

.sl-wrapper {
  flex: 1;
  position: relative;
}

.summary-section {
  .highlight-red {
    input {
      background: #ff9393;
    }
  }

  .highlight-green {
    input {
      background: #6feda1;
    }
  }

  .app-table {
    td {
      .col-form-control {
        width: 25%;
      }
    }
  }
}

.bg-red {
  background: #ff9393;
}

.txt-red {
  color: #ff9393;
}

.bg-green {
  background: #6feda1;
}

.txt-green {
  color: #6feda1;
}

.bg-yellow {
  background: #f2e56f;
}

.txt-yellow {
  color: #f2e56f;
}

.bg-blue {
  background: #196aaa;
}

.txt-blue {
  color: #196aaa;
}

.bg-map-purple {
  background: #948ce1;
}

.bg-map-yellow {
  background: #e8bf53;
}

.bg-map-green {
  background: #59d37b;
}

.bg-map-red {
  background: #ff9393;
}

.green {
  background: #6feda1 !important;
  color: #6feda1 !important;
}

.red {
  background: #ff9393 !important;
  color: #ff9393 !important;
}

.yellow {
  background: #f2e56f !important;
  color: #f2e56f !important;
}

.grey {
  background: #adb2b9 !important;
  color: #adb2b9 !important;
}

.indicators > div {
  div {
    flex: 1;
    width: 100%;
  }

  .survey-value {
    clip-path: polygon(0 0, 104% 0, 100% 100%);
    position: relative;

    &:after {
      content: "";
      position: absolute;
      border-top: 1px solid #fff;
      transform: rotate(18deg);
      transform-origin: 2% 0%;
      width: 100%;
      top: 0;
      left: 0px;
    }
  }
}

.indicators-dashboard {
  .indicators {
    .status-box {
      min-height: 35px;
    }
  }

  tbody tr td:nth-child(1) {
    min-width: 150px !important;
    padding: 2px 10px;
    min-width: 150px;
    max-width: 350px;
  }

  thead th:nth-child(1) {
    min-width: 150px !important;
    padding: 2px 10px;
    min-width: 150px;
    max-width: 350px;
  }

  tbody tr td:nth-child(2) {
    position: sticky;
    left: 145px;
    min-width: 150px;
    max-width: 350px;
    background-color: #fff;
    z-index: 99;
    border-left: 1px solid #ececec;
    padding: 2px 10px;
  }

  thead th:nth-child(2) {
    position: sticky;
    left: 145px;
    min-width: 150px;
    max-width: 350px;
    background-color: #f3f6f8;
    z-index: 99;
    border-left: 1px solid #ececec;
    padding: 2px 10px;
  }

  tbody tr td:nth-child(3) {
    position: sticky;
    left: 280px;
    min-width: 120px;
    max-width: 350px;
    background-color: #fff;
    z-index: 99;
    border-left: 1px solid #ececec;
    padding: 2px 10px;
  }

  thead th:nth-child(3) {
    position: sticky;
    left: 280px;
    min-width: 120px;
    max-width: 350px;
    background-color: #f3f6f8;
    z-index: 99;
    border-left: 1px solid #ececec;
    padding: 2px 10px;
  }

  tbody tr td {
    padding: 1px 1px;
    min-width: 100px;
    text-align: left;
    vertical-align: top;
  }

  thead th {
    border-left: 1px solid #fff;
    border-top: 1px solid #fff;
    text-align: left;
  }
}

.diagram {
  -webkit-background-size: contain;
  -moz-background-size: contain;
  -o-background-size: contain;
  background-size: contain;
  min-height: 900px;
  background-position: center center;
  background-repeat: no-repeat;
}

.HMIS {
  background-image: url(./images/HMIS.jpg);
}

.HMIS_Only {
  background-image: url(./images/HMIS_Only.jpg);
}

.HMIS_FullRepository_CorePrinciples {
  background-image: url(./images/HMIS_FullRepository_CorePrinciples.jpg);
}

.HMIS_FullRepository {
  background-image: url(./images/HMIS_FullRepository.jpg);
}

.HMIS_SimplifiedRepository {
  background-image: url(./images/HMIS_SimplifiedRepository.jpg);
}

.NationalMalariaRepository {
  background-image: url(./images/NationalMalariaRepository.jpg);
}

.KeyDataSystemComponent {
  background-image: url(./images/KeyDataSystemComponent.jpg);
}

.HMIS_HealthManagementInformationSystem {
  background-image: url(./images/HMIS_HealthManagementInformationSystem.jpeg);
}

.HMIS_fr {
  background-image: url(./images/HMIS_fr.png);
}

.HMIS_Only_fr {
  background-image: url(./images/HMIS_Only_fr.png);
}

.HMIS_FullRepository_CorePrinciples_fr {
  background-image: url(./images/HMIS_FullRepository_CorePrinciples_fr.png);
}

.HMIS_FullRepository_fr {
  background-image: url(./images/HMIS_FullRepository_fr.png);
}

.NationalMalariaRepository_fr {
  background-image: url(./images/NationalMalariaRepository_fr.png);
}

.KeyDataSystemComponent_fr {
  background-image: url(./images/KeyDataSystemComponent_fr.png);
}

.HMIS_HealthManagementInformationSystem_fr {
  background-image: url(./images/HMIS_HealthManagementInformationSystem_fr.png);
}

p.wrapText {
  word-wrap: break-word;
}

.disableContent {
  opacity: 0.7;

  label {
    opacity: 0.7;
  }
}

.disableCalendarContent {
  pointer-events: none;
  opacity: 0.7;
}

.grid-wrapper {
  table {
    tr th:nth-last-child(1) {
      width: 22% !important;
    }

    tr th:nth-last-child(2) {
      width: 15% !important;
    }

    tr th:nth-child(3) {
      width: 10% !important;
    }

    tr {
      td:nth-last-child(1) {
        width: 22% !important;
      }

      td:nth-last-child(2) {
        width: 15% !important;
      }

      td:nth-child(3) {
        width: 10% !important;
      }
    }
  }
}

.user-grid-wrapper {
  .k-grid-header .k-header:nth-last-child(2) .grid-icon-hide {
    display: none;
  }

  table {
    tr th:nth-last-child(1) {
      width: 22% !important;
    }

    tr th:nth-last-child(2) {
      width: 15% !important;
    }

    tr {
      td:nth-last-child(1) {
        width: 22% !important;
      }

      td:nth-last-child(2) {
        width: 15% !important;
      }
    }
  }
}

.k-grid-calculation {
  .k-grid-container {
    .k-grid-content {
      .k-grid-table {
        margin-bottom: 15px;

        tr:last-child {
          background: rgba(33, 33, 33, 0.08);
        }

        tr {
          td:last-child {
            text-align: left;
          }
        }
      }
    }
  }
}

.page-report-section {
  .k-grid-wrapper {
    .k-grid-header .k-header.k-grid-header-sticky,
    .k-grid-header .k-filter-row .k-grid-header-sticky,
    .k-grid-content-sticky,
    .k-grid-row-sticky,
    .k-grid-footer-sticky {
      position: sticky;
      z-index: 2;
      white-space: normal;
    }

    .k-grid-header .k-header {
      /*white-space: normal;*/
      //TODO: If client ask for it
    }
  }
}

.hide-grid-icon {
  .k-grid-header .k-header {
    .grid-icon-hide {
      display: none;
    }
  }
}

.k-grid .k-grid-content-sticky {
  border-right-width: 0px;
}

.k-master-row:hover .k-grid-content-sticky {
  background-color: #fff;
}

.k-grid-calculation .k-master-row:last-child td {
  background-color: #ececec;
  white-space: normal;
  font-weight: bold;
}

.both .k-master-row:nth-last-child(2) td {
  background-color: #ececec;
  white-space: normal;
  font-weight: bold;
}

.country-progress-box {
  margin-right: 1rem;
  height: 10px;
  border-radius: 1rem;
}

.title-progress-box {
  padding: 1rem 1rem 0rem 2rem;
}

.sub-progress-box {
  padding: 1rem 1rem 1rem 2rem;
}

/*Card CSS*/
.countyr-card-wrapper {
  .card {
    border: 1px solid #e1e1e1;
    background-color: rgb(244, 250, 250);
  }

  .card-title {
    font-size: 16px;
    font-weight: 700;
    color: #383838;
  }

  .card-text {
    color: #686868;
    margin-bottom: 0px;
  }
}

.objective-score-wrapper {
  border-top: 1px solid #ddd;
  border-bottom: 1px solid #ddd;
  color: #646464 !important;
  padding: 10px;
}

.custom-progress-box {
  border-radius: 4.5rem;
  background-color: "#ADB2B9";
}

.custom-progress-bar-title {
  font-weight: 700;
}

.worldMapWrapper svg {
  display: inline-block;
  vertical-align: middle;
  background-color: white !important;
  border-radius: 20px;
}

/* Dashboard legends styling */
.dashboard-legend {
  list-style: none;
}

.dashboard-legend li {
  float: left;
  margin-right: 15px;
}

.dashboard-legend span {
  float: left;
  width: 12px;
  height: 12px;
  margin: 2px;
  top: 3px;
  position: relative;
  left: -3px;
}

.dashboard-legend .not-assessed {
  background-color: #adb2b9;
}

.dashboard-legend .met {
  background-color: #6feda1;
}

.dashboard-legend .not-met {
  background-color: #ff9393;
}

.dashboard-legend .partially-met {
  background-color: #f2e56f;
}

.dashboard-legend .no-data {
  background-color: #eeeeee;
}

.dashboard-legend .disputed {
  background-color: #A9A9A9;
}

table#score-card-table th#score {
  text-align: center;
}

#score-card-table tbody tr td .inputfocus select {
  padding: 8px 32px 8px 8px;
}

.section-bg-content app-btn-secondary:hover {
  background-color: #ccc;
  color: #196aaa !important;
}

.page-report-section {
  .MuiTabs-vertical {
    .app-tab {
      text-align: left;
      min-height: 50px !important;
    }
  }
}

#elimination-dqa-summary-section {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 20px;
}

#elimination-dqa-year-margin {
  margin-bottom: 10px;
}

.customtextarea {
  div {
    height: 150px !important;

    fieldset {
      height: 150px;
    }
  }

  .MuiInputBase-root {
    font-size: 1rem !important;
    align-items: baseline !important;
  }
}

.que_3-2-1 {
  margin-top: -73px;
  margin-bottom: 8px;
  padding-left: 14px;

  div {
    color: #9e9e9e;
    font-family: "Roboto", "Helvetica", "Arial", sans-serif;
    line-height: 1.1876em;
    letter-spacing: 0.00938em;
    font-size: 15px;
  }
}

.required-star {
  position: absolute;
  padding-left: 9px;
  padding-top: 4px;
  font-weight: bolder;
}

.read-only-textbox {
  height: 38px;
  border: 1px solid;
  width: 61px;
  padding: 5px;
  margin: 0;
  position: absolute;
}

.nav-arrow-btn {
  color: #ccc;
  cursor: pointer;
  position: absolute;
  top: 22px;
}

.nav-arrow-btn:hover {
  color: #008dc3 !important;
}

.nav-arrow-btn-prev {
  left: 30px;
}

.nav-arrow-btn-next {
  right: 24px;
}

.disableContent #score-card-table .status-column label {
  opacity: 1;
  font-weight: bold;
}

#score-card-table .status-column label {
  opacity: 1;
  font-weight: bold;
}

.cellWrapText {
  overflow-wrap: break-word;
  word-wrap: break-word;
  word-break: break-word;
  white-space: break-spaces !important;
}

.summary-wrapper .MuiInputBase-root.Mui-disabled .expand-icon {
  display: none;
}

.warning-panel {
  background: #ffffcc !important;
  border: 1px solid #ffc107;
  padding: 10px;
  margin-top: 5px;
  margin-bottom: 5px;
  font-family: Verdana, sans-serif;
}

// .k-table {
//     colgroup{
//       display: flex;
//     }
// }

// .k-grid-header{
//   padding-right: 0px;
// }

// .k-table-thead{
//   display: block;
//   .k-table-row{
//     display: flex;
//     justify-content: space-between;
//     margin-left: 10px;
//     td{
//      padding: 10px;
//       width: 250px;
//       border-left: 1px solid #dee2e6;
//       &:first-child{
//         border-left: 0;
//       }
//     }
//     th{
//       width: 250px;
//     }
//     .w-250{
//       width: 250px;
//     }
//     .MuiInputBase-root{
//       height: 26px;
//     }
//     input{
//       width: 100%;
//     }
//     select{
//       width: 100%;
//       padding: 10px;
//     }
//   }
// }
// .k-grid-header{
//   padding-right: 0px !important;
// .k-filter-row{
//   background: white;
//   margin-left: 0px !important;
// }
// .MuiSvgIcon-root{
//   width: 20px !important;
// }
// }


.k-grid{
  .k-grid-header{
      padding-right: 0px !important;
      .k-filter-row{
        background: white;
        margin-left: 0px !important;
      }
      .MuiSvgIcon-root{
        width: 20px !important;
      }
      .MuiButtonBase-root{
        padding-top: 0px;
        padding-bottom: 0px;
      }
    .k-grid-header-table{
      .k-table-thead{
        .k-filter-row {
            .k-textbox {
              background: white;
              border: none;
              border-bottom: 1px solid rgba(0, 0, 0, 0.23);
              padding: 2px 8px;
              .k-input-inner{
                padding: 0px;
              }
               &:focus-visible{
               outline: none !important;
            }
            }
          }
        .MuiInputBase-root{
          height: 26px;
        }
        input{
          width: 100%;
        }
        select{
          width: 100%;
          padding: 10px;
        }
        .k-table-row{
         
          td{
            border-left: 1px solid #dee2e6;
            &:first-child{
              border-left: 0;
            }
          }
        }
      }
    }
  }
}
.k-table th {
    padding: 8px 12px;
}

.k-grid-content {
    overflow-y: auto;
}

.k-grid-norecords-template{
  margin-top: 15px;
}

.k-grid-content {
  .k-table-row:last-child {
    > td,
    > .k-table-td {
      border-bottom-width: 1px;
    }
  }
}

.MuiStepper-root{
    margin-top: 1.3rem !important;
    margin-bottom: 1rem !important;
}

.css-5h82ro-MuiInputBase-root-MuiInput-root::before {
    border-bottom: 1px solid rgba(0, 0, 0, 0.23) !important;
}
.css-5h82ro-MuiInputBase-root-MuiInput-root::after {
    border-bottom: 1px solid rgba(0, 0, 0, 0.23) !important
}