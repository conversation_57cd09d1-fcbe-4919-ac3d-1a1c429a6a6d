﻿
import { StatusCode } from "./Enums";


export class BaseMessageModel {
    constructor(public text: string, public statusCode: number) {
        this.text = text;
        this.statusCode = statusCode;
    }
    
    get isSuccessful(): boolean {
        return this.statusCode === StatusCode.Created || this.statusCode === StatusCode.Ok;
    }

    static init = () => new BaseMessageModel("", StatusCode.Ok);
}

export class ErrorModel extends BaseMessageModel {
    constructor(public text: string, public statusCode: number) {
       super(text, statusCode);       
    }

    static init = () => new BaseMessageModel("", StatusCode.Ok);
}

