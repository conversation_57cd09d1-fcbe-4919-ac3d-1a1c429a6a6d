export class Response_1 {
  constructor(
    public cannotBeAssessed: boolean | false,
    public cannotBeAssessedReason: string | null,
    public metNotMetStatus: string | null,
    public accessUsers: Array<AccessUser>,
    public canAccessNational: boolean = false,
    public canAccessSubNational: boolean = false,
    public canAccessServiceDelivery: boolean = false
  ) {
    this.cannotBeAssessed = cannotBeAssessed;
    this.cannotBeAssessedReason = cannotBeAssessedReason;
    this.metNotMetStatus = metNotMetStatus;
    this.accessUsers = accessUsers;
    this.canAccessNational = canAccessNational;
    this.canAccessSubNational = canAccessSubNational;
    this.canAccessServiceDelivery = canAccessServiceDelivery;
  }

  static init = () =>
    new Response_1(false, null, null, [], false, false, false);
}

export class AccessUser {
  constructor(
    public dataAccessDetails: string | null,
    public national: string | null,
    public subNational: string | null,
    public serviceDelivery: string | null
  ) {
    this.dataAccessDetails = dataAccessDetails;
    this.national = national;
    this.subNational = subNational;
    this.serviceDelivery = serviceDelivery;
  }
}
