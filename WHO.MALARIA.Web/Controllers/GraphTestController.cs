using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.Threading.Tasks;
using WHO.MALARIA.Services;
using Microsoft.AspNetCore.Http;
using WHO.MALARIA.Features.Helpers;
using WHO.MALARIA.Domain.Enum;
using System.Collections.Generic;
using Microsoft.Graph;
using Microsoft.Kiota.Abstractions;
using System.Net;
using WHO.MALARIA.Web.Apis;
using MediatR;
using System.Linq;
using WHO.MALARIA.Domain.Constants;

namespace WHO.MALARIA.Web.Controllers
{
    /// <summary>
    /// Test controller to verify Graph API permissions and diagnose invitation issues
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class GraphTestController : BaseApiController
    {
        private readonly ILogger<GraphTestController> _logger;
        private readonly IGraphService _graphService;

        public GraphTestController(
            ILogger<GraphTestController> logger,
            IGraphService graphService,
            IMediator mediator,
            IHttpContextAccessor httpContextAccessor) : base(mediator, httpContextAccessor)
        {
            _logger = logger;
            _graphService = graphService;
        }

        /// <summary>
        /// Test Graph API permissions and access token
        /// </summary>
        [HttpGet("test-permissions")]
        public async Task<IActionResult> TestGraphPermissions()
        {
            try
            {
                var currentUser = GetCurrentUser();
                if (!currentUser.IsAuthenticated)
                {
                    return BadRequest(new { error = "No current user found. Please login first." });
                }

                // Get email from claims
                var email = GetUserEmail();
                if (string.IsNullOrEmpty(email))
                {
                    return BadRequest(new { error = "No email found in user claims." });
                }

                // Use the new comprehensive test method
                var graphApiTestResult = await _graphService.TestGraphApiAccess(email);

                var result = new
                {
                    CurrentUser = new
                    {
                        UserId = currentUser.UserId,
                        Email = email,
                        Name = currentUser.Name,
                        UserType = currentUser.UserType,
                        IsAuthenticated = currentUser.IsAuthenticated
                    },
                    AccessTokenInfo = GetAccessTokenInfo(),
                    GraphApiTestResult = graphApiTestResult
                };

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error testing Graph API permissions");
                return StatusCode(500, new { error = ex.Message, stackTrace = ex.StackTrace });
            }
        }

        /// <summary>
        /// Test sending a B2B invitation to a specific email
        /// </summary>
        [HttpPost("test-invitation")]
        public async Task<IActionResult> TestInvitation([FromBody] TestInvitationRequest request)
        {
            try
            {
                var currentUser = GetCurrentUser();
                if (!currentUser.IsAuthenticated)
                {
                    return BadRequest(new { error = "No current user found. Please login first." });
                }

                // Get email from claims
                var currentUserEmail = GetUserEmail();
                if (string.IsNullOrEmpty(currentUserEmail))
                {
                    return BadRequest(new { error = "No email found in user claims." });
                }

                if (string.IsNullOrEmpty(request.Email))
                {
                    return BadRequest(new { error = "Email is required" });
                }

                _logger.LogInformation($"Testing invitation for email: {request.Email}");

                // Test the invitation
                var invitationResult = await _graphService.SendInvitation(
                    request.FirstName ?? "Test",
                    request.LastName ?? "User",
                    request.Email,
                    "This is a test invitation from the Malaria Surveillance Tool",
                    $"{HttpContext.Request.Scheme}://{HttpContext.Request.Host}/test-callback",
                    currentUserEmail,
                    "en"
                );

                var result = new
                {
                    InvitationStatus = invitationResult.Item1.ToString(),
                    RedeemUrl = invitationResult.Item2,
                    TestEmail = request.Email,
                    CurrentUser = currentUserEmail,
                    AccessTokenInfo = GetAccessTokenInfo()
                };

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error testing invitation for {request.Email}");
                return StatusCode(500, new {
                    error = ex.Message,
                    stackTrace = ex.StackTrace,
                    innerException = ex.InnerException?.Message
                });
            }
        }

        /// <summary>
        /// Test checking if a user exists in Azure AD
        /// </summary>
        [HttpPost("test-user-exists")]
        public async Task<IActionResult> TestUserExists([FromBody] TestUserExistsRequest request)
        {
            try
            {
                var currentUser = GetCurrentUser();
                if (!currentUser.IsAuthenticated)
                {
                    return BadRequest(new { error = "No current user found. Please login first." });
                }

                // Get email from claims
                var currentUserEmail = GetUserEmail();
                if (string.IsNullOrEmpty(currentUserEmail))
                {
                    return BadRequest(new { error = "No email found in user claims." });
                }

                if (string.IsNullOrEmpty(request.Email))
                {
                    return BadRequest(new { error = "Email is required" });
                }

                _logger.LogInformation($"Testing user existence for email: {request.Email}");

                var userExistsStatus = await _graphService.IsUserExistingInAzureAD(request.Email, currentUserEmail);

                var result = new
                {
                    UserExistsStatus = userExistsStatus.ToString(),
                    TestEmail = request.Email,
                    CurrentUser = currentUserEmail,
                    AccessTokenInfo = GetAccessTokenInfo()
                };

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error testing user existence for {request.Email}");
                return StatusCode(500, new {
                    error = ex.Message,
                    stackTrace = ex.StackTrace,
                    innerException = ex.InnerException?.Message
                });
            }
        }

        private string GetUserEmail()
        {
            try
            {
                var emailClaim = HttpContext.User.Claims
                    .FirstOrDefault(c => c.Type.Equals(WHO.MALARIA.Domain.Constants.Constants.IdentityClaims.Email, StringComparison.InvariantCultureIgnoreCase));
                return emailClaim?.Value ?? string.Empty;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting user email from claims");
                return string.Empty;
            }
        }

        private object GetAccessTokenInfo()
        {
            try
            {
                string accessToken = HttpContext.Request.Cookies["access_token"];

                if (string.IsNullOrEmpty(accessToken))
                {
                    return new { hasAccessToken = false, error = "No access token found in cookies" };
                }

                // Basic token info (don't expose the actual token for security)
                return new
                {
                    hasAccessToken = true,
                    tokenLength = accessToken.Length,
                    tokenPrefix = accessToken.Substring(0, Math.Min(10, accessToken.Length)) + "...",
                    cookieExists = true
                };
            }
            catch (Exception ex)
            {
                return new { hasAccessToken = false, error = ex.Message };
            }
        }


    }

    public class TestInvitationRequest
    {
        public string Email { get; set; }
        public string FirstName { get; set; }
        public string LastName { get; set; }
    }

    public class TestUserExistsRequest
    {
        public string Email { get; set; }
    }
}
