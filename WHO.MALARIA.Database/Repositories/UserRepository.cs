﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading.Tasks;
using Dapper;
using Microsoft.AspNetCore.Http;
using WHO.MALARIA.Database.IRepositories;
using WHO.MALARIA.Domain.Constants;
using WHO.MALARIA.Domain.Dtos;
using WHO.MALARIA.Domain.Dtos.InputDtos;
using WHO.MALARIA.Domain.Dtos.OutputDtos;
using WHO.MALARIA.Domain.Enum;
using WHO.MALARIA.Domain.Models.Identity;
using WHO.MALARIA.Features.Helpers;

namespace WHO.MALARIA.Database.Repositories
{
    public class UserRepository : Repository<User>, IUserRepository
    {
        private readonly IDbManager _dbManager;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private string _language => _httpContextAccessor?.HttpContext?.Request?.Cookies[Constants.Common.I18next] ?? Constants.Common.DefaultLanguage;

        public UserRepository(IDbManager dbManager, MalariaDbContext dbContext, IHttpContextAccessor httpContextAccessor) : base(dbContext)
        {
            _dbManager = dbManager;
            _httpContextAccessor = httpContextAccessor;
        }

        /// <summary>
        /// Retrieve all users
        /// </summary>
        /// <param name="onlyActive">When passed false, all the users will be returned irrespective of there status and
        /// When passed true, only active users will be returned"</param>
        /// <returns>List of UserDto object</returns>
        public async Task<IEnumerable<UserDto>> GetAllUsersAsync(bool onlyActive = false)
        {
            string sql = $"SELECT U.Id, U.Status, U.Name,{MalariaSchemas.Internal}.GetUserType(U.Id) AS UserType, I.Email FROM [{MalariaSchemas.Internal}].[User] U"
                + $" INNER JOIN [{MalariaSchemas.Internal}].[Identity] I ON U.IdentityId = I.Id";

            // if active then filter out only active users
            if (onlyActive)
            {
                sql = string.Concat(sql, " ", $"WHERE U.Status = {(int)UserStatus.Active}");
            }

            IEnumerable<UserDto> users = await _dbManager.QueryAsync<UserDto>(sql);

            return users;
        }

        /// <summary>
        /// Retrieve List of Non-WHO users
        /// </summary>
        /// <param name="inputDto">An Object of GetNonWHOUsersInputDto</param>
        /// <returns>List of non-users</returns>
        public async Task<IEnumerable<UserDto>> GetNonWHOUsersAsync(GetNonWHOUsersInputDto inputDto)
        {
            string sql = $"SELECT U.Id, U.Status, U.IdentityId, U.Name, U.OrganizationName, UCA.UserType, I.Email " +
                $"FROM [{MalariaSchemas.Internal}].[User] U " +
                $"INNER JOIN [{MalariaSchemas.Internal}].[Identity] I ON I.Id = U.IdentityId " +
                $"INNET JOIN [{MalariaSchemas.Internal}].[UserCountryAccess] UCA ON UCA.UserId = U.Id " +
                $"WHERE UCA.CountryId = @CountryId and U.UserId <> {inputDto.UserId}";

            IEnumerable<UserDto> users = await _dbManager.QueryAsync<UserDto>(sql, new { CountryId = inputDto.CountryId });

            return users;
        }

        /// <summary>
        /// Returns object with the list of user activation requests and user country access pending requests
        /// </summary>
        /// <param name="countryId">country id of the user calling the api</param>
        /// <returns>Returns PendingRequestsOutputDto object</returns>
        public async Task<PendingRequestsOutputDto> GetPendingRequestsAsync(Guid countryId)
        {
            string sql = $"SELECT U.Id, U.Status, U.IdentityId, U.Name, U.OrganizationName, UCA.UserType, I.Email, C.Name AS Country, C.Id AS CountryId, " +
                         $"U.Status, UCA.Status AS CountryAccessStatus, UCA.Id AS UserCountryAccessId " +
                         $"FROM [{MalariaSchemas.Internal}].[User] U " +
                         $"INNER JOIN [{MalariaSchemas.Internal}].[Identity] I ON I.Id = U.IdentityId " +
                         $"INNER JOIN [{MalariaSchemas.Internal}].[UserCountryAccess] UCA ON UCA.UserId = U.Id " +
                         $"INNER JOIN [{MalariaSchemas.Internal}].[Country] C ON UCA.CountryId=C.Id " +
                         $"WHERE C.Id= @CountryId " +
                         $"AND [UCA].[UserType]=@UserType AND (U.Status=@UserStatus OR UCA.Status=@CountryAccessStatus)";

            IEnumerable<PendingRequestUserDto> users = await _dbManager.QueryAsync<PendingRequestUserDto>(sql, new
            {
                UserStatus = (int)UserStatus.Pending,
                CountryAccessStatus = (int)UserCountryAccessRightsEnum.Pending,
                UserType = (int)UserRoleEnum.Viewer,
                CountryId = countryId
            });

            return new PendingRequestsOutputDto()
            {
                UserActivationRequests = users.Where(u => u.Status == (int)UserStatus.Pending && u.CountryAccessStatus != (int)UserCountryAccessRightsEnum.InvitationNotAccepted),
                CountryAccessRequests = users.Where(u => u.CountryAccessStatus == (int)UserCountryAccessRightsEnum.Pending && u.Status == (int)UserStatus.Active)
            };
        }

        /// <summary>
        /// Retrieve user by it's id
        /// </summary>
        /// <param name="id">user id</param>
        /// <returns>UserDto object</returns>
        public async Task<UserDto> GetUserByIdAsync(Guid id)
        {
            string sql = $"SELECT U.Id, U.Status, U.IdentityId, U.Name, U.OrganizationName, U.IsWhoAdmin, I.Email " +
                $"FROM [{MalariaSchemas.Internal}].[User] U " +
                $"INNER JOIN [{MalariaSchemas.Internal}].[Identity] I ON I.Id = U.IdentityId " +
                "WHERE U.Id = @Id";

            UserDto user = await _dbManager.QuerySingleAsync<UserDto>(sql, new { Id = id });

            return user;
        }

        /// <summary>
        /// Retrieve multiple user by it's identity id
        /// </summary>
        /// <param name="identityId">identity id</param>
        /// <returns>UserDto object</returns>
        public async Task<IEnumerable<UserDto>> GetUsersByIdentityIdAsync(Guid identityId)
        {
            string sql = $"SELECT U.Id, U.UserType, U.Status, {MalariaSchemas.Internal}.GetUserType(U.Id) AS UserType " +
                $"FROM [{MalariaSchemas.Internal}].[User] U " +
                "WHERE U.IdentityId = @IdentityId";

            IEnumerable<UserDto> users = await _dbManager.QueryAsync<UserDto>(sql, new { IdentityId = identityId });

            return users;
        }

        /// <summary>
        /// Retrieve user by email
        /// </summary>
        /// <param name="email">User email</param>
        /// <returns>UserDto object</returns>
        public async Task<UserDto> GetUserByEmailAsync(string email)
        {
            string sql = $"SELECT U.Id, {MalariaSchemas.Internal}.GetUserType(U.Id) AS UserType, U.Status, U.IdentityId, U.Name, U.OrganizationName, I.Email " +
                            $"FROM [{MalariaSchemas.Internal}].[User] U " +
                            $"INNER JOIN [{MalariaSchemas.Internal}].[Identity] I ON I.Id = U.IdentityId " +
                            "WHERE I.Email = @Email";

            UserDto user = await _dbManager.QuerySingleAsync<UserDto>(sql, new { Email = email });

            return user;
        }

        /// <summary>
        /// Returns users for WHO requester
        /// </summary>
        /// <param name="currentUserId">User Id of current user</param>
        /// <returns>Enumerable of UserDto objects</returns>
        public async Task<IEnumerable<UserDto>> GetUsersForWHORequesterAsync(Guid currentUserId)
        {
            string sql = @$"SELECT U.Id, 
                                   UCA.UserType, 
                                   UCA.Status,
                                   U.IdentityId, 
                                   U.Name, 
                                   U.OrganizationName, 
                                   I.Email, 
                                   C.Name AS Country, 
                                   C.Id AS CountryId
                                   FROM [{MalariaSchemas.Internal}].[User] U 
                                   INNER JOIN [{ MalariaSchemas.Internal}].[Identity] I ON I.Id = U.IdentityId
                                   INNER JOIN [{ MalariaSchemas.Internal}].[UserCountryAccess] UCA ON UCA.UserId = U.Id
                                   INNER JOIN [{ MalariaSchemas.Internal}].[Country] C ON UCA.CountryId = C.Id
                                   WHERE UCA.UserType=@UserTypeForSuperManager OR (UCA.UserType=@UserTypeForViewer and U.Status = @UserStatus and I.Status = @IdentityStatus and UCA.Status = @UserCountryAccessStatus) AND U.IsWhoAdmin = 0
                          UNION 
                          SELECT U.Id,
                                 UserType = {(int)UserRoleEnum.WHOAdmin}, 
                                 U.Status, 
                                 U.IdentityId, 
                                 U.Name,
                                 U.OrganizationName, 
                                 I.Email, 
                                 NULL AS Country, 
                                 NULL AS CountryId
                         FROM [Internal].[User] U 
                         INNER JOIN [Internal].[Identity] I ON I.Id = U.IdentityId
                         WHERE  U.IsWhoAdmin = 1 AND U.Id != @CurrentUserId";

            IEnumerable<UserDto> users = await _dbManager.QueryAsync<UserDto>(sql,
                new
                {
                    UserTypeForSuperManager = (int)UserRoleEnum.SuperManager,
                    UserTypeForViewer = (int)UserRoleEnum.Viewer,
                    UserStatus = (int)UserStatus.Active,
                    IdentityStatus = (int)IdentityStatus.Active,
                    UserCountryAccessStatus = (int)UserCountryAccessRightsEnum.Accepted,
                    CurrentUserId = currentUserId
                });

            return users;
        }

        /// <summary>
        /// Get newly registered in active viewers
        /// </summary>
        /// <param name="currentUserId">User Id of current user</param>
        /// <returns>List of newly registered users</returns>
        public async Task<IEnumerable<UserDto>> GetNewlyRegisteredInActiveViewersAsync(Guid currentUserId)
        {
            string sql = @$"SELECT U.Id, 
                                   -1 AS UserType, 
                                   UCA.Status,
                                   U.IdentityId, 
                                   U.Name, 
                                   U.OrganizationName, 
                                   I.Email, 
                                   C.Name AS Country, 
                                   C.Id AS CountryId,
                                   UCA.RejectionComment AS Comment,
                                   UCA.Id AS UserCountryAccessId
                                   FROM [{MalariaSchemas.Internal}].[User] U 
                                   INNER JOIN [{ MalariaSchemas.Internal}].[Identity] I ON I.Id = U.IdentityId
                                   INNER JOIN [{ MalariaSchemas.Internal}].[UserCountryAccess] UCA ON UCA.UserId = U.Id
                                   INNER JOIN [{ MalariaSchemas.Internal}].[Country] C ON UCA.CountryId = C.Id
                                   WHERE U.Status = @UserStatus and I.Status = @IdentityStatus AND UCA.UserType = @UserType AND UCA.Status = @UserCountryAccessStatus AND U.IsWhoAdmin = 0  AND U.Id != @CurrentUserId
                                   ";

            IEnumerable<UserDto> users = await _dbManager.QueryAsync<UserDto>(sql,
                new
                {
                    UserStatus = (int)UserStatus.Pending,
                    IdentityStatus = (int)IdentityStatus.InActive,
                    UserCountryAccessStatus = (int)UserCountryAccessRightsEnum.Pending,
                    UserType = (int)UserRoleEnum.Viewer,
                    CurrentUserId = currentUserId
                });

            return users;
        }

        /// <summary>
        /// Returns users for SuperManager requester
        /// </summary>
        /// <param name="countryId">country Id of current user</param>
        /// <returns>Enumerable of UserDto objects</returns>
        public async Task<IEnumerable<UserDto>> GetUsersForSuperManagerRequesterAsync(Guid countryId)
        {
            string sql = @$"SELECT U.Id, 
                                   UCA.UserType, 
                                   UCA.Status,
                                   U.IdentityId,
                                   U.Name, U.OrganizationName, 
                                   I.Email, C.Id as CountryId, 
                                   C.Name AS Country 
                            FROM [{ MalariaSchemas.Internal}].[Identity] I 
                            INNER JOIN  [{MalariaSchemas.Internal}].[User] U ON  U.IdentityId = I.Id
                            INNER JOIN [{ MalariaSchemas.Internal}].[UserCountryAccess] UCA ON UCA.UserId = U.Id
                            INNER JOIN [{ MalariaSchemas.Internal}].[Country] C ON UCA.CountryId = C.Id
                            WHERE C.Id = @CountryId
                            AND UCA.UserType IN @UserTypes AND UCA.[Status] IN @AllowedUserCountryAccessStatuses AND U.[Status] IN @AllowedUserStatuses";

            IEnumerable<UserDto> users = await _dbManager.QueryAsync<UserDto>(sql,
                new
                {
                    UserTypes = new int[] {
                                (int)UserRoleEnum.Manager,
                                (int)UserRoleEnum.Viewer
                    },
                    CountryId = countryId,
                    AllowedUserStatuses = new int[] {
                                (int)UserStatus.Active,
                                (int)UserStatus.InActive,
                                 (int)UserStatus.Pending

                    },
                    AllowedUserCountryAccessStatuses = new int[] {
                                (int)UserCountryAccessRightsEnum.Accepted,
                                (int)UserCountryAccessRightsEnum.InActive,
                                (int)UserCountryAccessRightsEnum.InvitationNotAccepted
                    }
                });

            return users;
        }

        /// <summary>
        /// Returns all users of a country
        /// </summary>
        /// <param name="countryId">Id of country</param>
        /// <returns>Enumerable of UserDto objects</returns>
        public async Task<IEnumerable<UserDto>> GetAllUsersOfCountryAsync(Guid countryId)
        {
            string sql = @$"SELECT U.Id, UCA.UserType, U.Status, U.IdentityId, U.Name, U.OrganizationName, I.Email, C.Name AS Country 
                            FROM [{MalariaSchemas.Internal}].[User] U 
                            INNER JOIN [{ MalariaSchemas.Internal}].[Identity] I ON I.Id = U.IdentityId
                            INNER JOIN [{ MalariaSchemas.Internal}].[UserCountryAccess] UCA ON UCA.UserId = U.Id
                            INNER JOIN [{ MalariaSchemas.Internal}].[Country] C ON UCA.CountryId = C.Id AND UCA.[Status]=1
                            WHERE C.Id=@CountryId";

            IEnumerable<UserDto> users = await _dbManager.QueryAsync<UserDto>(sql,
                new
                {
                    CountryId = countryId
                });

            return users;
        }

        /// <summary>
        /// Returns all managers of a country
        /// </summary>
        /// <param name="countryId">Id of country</param>
        /// <returns>Enumerable of UserDto objects</returns>
        public async Task<IEnumerable<UserDto>> GetCountryManagersAsync(Guid countryId)
        {
            string sql = @$"SELECT U.Id, @ManagerUserType AS UserType, U.Status, U.IdentityId, U.Name, U.OrganizationName, I.Email, C.Name AS Country 
                            FROM [{MalariaSchemas.Internal}].[User] U 
                            INNER JOIN [{ MalariaSchemas.Internal}].[Identity] I ON I.Id = U.IdentityId
                            INNER JOIN [{ MalariaSchemas.Internal}].[UserCountryAccess] UCA ON UCA.UserId = U.Id
                            INNER JOIN [{ MalariaSchemas.Internal}].[Country] C ON UCA.CountryId = C.Id AND UCA.[Status]=1
                            WHERE C.Id=@CountryId AND UCA.UserType=@ManagerUserType";

            IEnumerable<UserDto> users = await _dbManager.QueryAsync<UserDto>(sql,
                new
                {
                    CountryId = countryId,
                    ManagerUserType = (int)UserRoleEnum.Manager
                });

            return users;
        }

        /// <summary>
        /// Returns all viewers of a country
        /// </summary>
        /// <param name="countryId">Id of country</param>
        /// <returns>Enumerable of UserDto objects</returns>
        public async Task<IEnumerable<UserDto>> GetCountryViewersAsync(Guid countryId)
        {
            string sql = @$"SELECT U.Id, @ViewerUserType AS UserType, U.Status, U.IdentityId, U.Name, U.OrganizationName, I.Email, C.Name AS Country 
                            FROM [{MalariaSchemas.Internal}].[User] U 
                            INNER JOIN [{ MalariaSchemas.Internal}].[Identity] I ON I.Id = U.IdentityId
                            INNER JOIN [{ MalariaSchemas.Internal}].[UserCountryAccess] UCA ON UCA.UserId = U.Id
                            INNER JOIN [{ MalariaSchemas.Internal}].[Country] C ON UCA.CountryId = C.Id AND UCA.[Status]=1
                            WHERE C.Id=@CountryId AND UCA.UserType=@ViewerUserType";

            IEnumerable<UserDto> users = await _dbManager.QueryAsync<UserDto>(sql,
                new
                {
                    CountryId = countryId,
                    ViewerUserType = (int)UserRoleEnum.Viewer
                });

            return users;
        }

        /// <summary>
        /// Returns all country for login user
        /// </summary>
        /// <param name="currentUserIdentity">Identity object of current user</param>
        /// <returns></returns>
        public async Task<IEnumerable<IdAndNameDto>> GetUserCountriesAsync(WHOIdentity currentUserIdentity)
        {
            string sql = string.Empty;
            if (int.Parse(currentUserIdentity.UserType) == (int)UserRoleEnum.WHOAdmin)
            {
                sql = $@"SELECT 
                        C.Id,C.Name 
                        FROM {MalariaSchemas.Internal}.Country C 
                        INNER JOIN {MalariaSchemas.Internal}.UserCountryAccess UCA 
                        ON UCA.CountryId = C.Id
                        WHERE UCA.Status = 1 AND  UCA.UserId =@UserId AND C.IsActive = 1 AND UCA.UserType = @ManagerRole";
            }
            else
            {
                sql = $@"SELECT 
                       C.Id,C.Name 
                       FROM {MalariaSchemas.Internal}.Country C 
                       JOIN {MalariaSchemas.Internal}.UserCountryAccess UCA 
                       ON UCA.CountryId = C.Id
                       WHERE UCA.Status = 1 AND C.IsActive = 1 AND 
                       UCA.UserId =@UserId AND UCA.UserType IN (@SuperManagerRole, @ManagerRole)";
            }

            IEnumerable<IdAndNameDto> countries = await _dbManager.QueryAsync<IdAndNameDto>(sql, new
            {
                UserId = currentUserIdentity.UserId,
                SuperManagerRole = (int)UserRoleEnum.SuperManager,
                ManagerRole = (int)UserRoleEnum.Manager
            });

            return countries;
        }

        /// <summary>
        /// Returns all country for those which don't have any super manager active
        /// </summary>
        /// <returns></returns>
        public async Task<IEnumerable<IdAndNameDto>> GetCountriesWithoutSuperManager()
        {
            string sql = $@"SELECT C.Id,{"C." + _language.GetNameColumn()} AS Name FROM {MalariaSchemas.Internal}.Country C
                             LEFT JOIN {MalariaSchemas.Internal}.UserCountryAccess UCA ON C.Id=UCA.CountryId 
                             AND UCA.UserType = @UserType AND UCA.Status = @Status 
                             WHERE C.IsActive = 1 AND UCA.Id IS NULL ORDER BY {"C." + _language.GetNameColumn()}";

            IEnumerable<IdAndNameDto> countries = await _dbManager.QueryAsync<IdAndNameDto>(sql, new
            {
                UserType = (int)UserRoleEnum.SuperManager,
                Status = (int)UserStatus.Active
            });

            return countries;
        }

        /// <summary>
        /// Get email addresses of the users
        /// </summary>
        /// <param name="userIds">Required to fetch email addresses</param>
        /// <returns>List of email address</returns>
        public async Task<IEnumerable<string>> GetEmailAddresses(IEnumerable<Guid> userIds)
        {
            string joinedUserIds = string.Join(',', userIds);
            string sql = @$"SELECT I.Email FROM {MalariaSchemas.Internal}.[User] U 
                            INNER JOIN STRING_SPLIT('{joinedUserIds}', ',') T ON U.Id = T.value
                            INNER JOIN {MalariaSchemas.Internal}.[Identity] I ON U.IdentityId = I.Id";

            return await _dbManager.QueryAsync<string>(sql);
        }

        /// <summary>
        /// Get unassigned countries for a user
        /// </summary>
        /// <returns>Countries</returns>
        public async Task<IEnumerable<IdAndNameDto>> GetUnassignedCountries(Guid userId)
        {
            string sql = $@"SELECT 
                             C.Id,
                             {"C." + _language.GetNameColumn()} AS Name 
                             FROM {MalariaSchemas.Internal}.Country C
                             WHERE  
                             NOT EXISTS
                             (
                              SELECT 
                              UC.CountryId  
                              FROM  {MalariaSchemas.Internal}.UserCountryAccess UC
                              WHERE UC.CountryId = C.Id and UC.UserId = @UserId
                             ) 
                             AND C.IsActive = 1 ORDER BY {"C." + _language.GetNameColumn()}";

            IEnumerable<IdAndNameDto> countries = await _dbManager.QueryAsync<IdAndNameDto>(sql, new
            {
                UserId = userId
            });

            return countries;
        }
    }
}
